"use client";

import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search, Plus, ArrowUpDown, ChevronLeft, ChevronRight } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { format, isSameDay } from "date-fns";
import { setSearchQuery, setSelectedPatient } from "@/store/dentalSlice";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { GET_PATIENTS_BY_CLINIC } from "@/constants/apiRoutes";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { addPatientName } from "@/store/fullMouthSeriesSlice";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import {
  clearPreviousVisitLoadedImagesCompleteData,
  clearPreviousVisitLoadedXraysList,
  resetPreviousVisitClicked,
} from "@/store/previousVisits";

type Patient = {
  id?: number;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  crmId?: string;
  gender?: string;
  dob?: string;
  phoneNumber?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
  status?: string;
  visitDate?: string; // Added for potential visit date filtering
};

type FilterStatus = "all" | "Active" | "Inactive";

export default function PatientsPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const date = searchParams.get("date"); // Read date query parameter

  const searchQuery = useAppSelector((s) => s.dental.searchQuery);
  const userId = useAppSelector((s) => s.user.userId);
  const clinicId = useAppSelector((s) => s.user.clinicId);
  const today = format(new Date(), "yyyy-MM-dd");
  const isPastDate = !!date && date < today;

  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");
  const [isReversed, setIsReversed] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [patientsPerPage] = useState(10);

  const fetchPatients = async () => {
    try {
      if (!clinicId) {
        throw new Error("No clinicId found");
      }
      const url = `${GET_PATIENTS_BY_CLINIC}${encodeURIComponent(clinicId.toString())}`;
      console.log("Fetching patients from:", url);
      const response = await fetchWithRefresh(url, {}, router);

      if (!response) return; // Redirected to login

      if (!response.ok) {
        // If the response is not OK, try to parse error message
        let errorMessage = "Failed to fetch patients.";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          // If JSON parsing fails, use generic message
          console.warn("Failed to parse error response:", parseError);
        }
        // If status is 404 or 204, it might mean no patients, not an error
        if (response.status === 404 || response.status === 204) {
          setPatients([]); // Treat as no patients found
          setError(null); // Clear any previous error
          return;
        }
        throw new Error(errorMessage);
      }

      const json = await response.json();
      console.log("📥 raw patients payload:", json);

      let list: any[] = [];
      if (Array.isArray(json)) {
        list = json;
      } else if (Array.isArray(json.patients)) {
        list = json.patients;
      } else if (Array.isArray(json.data)) {
        list = json.data;
      } else {
        // If the response is not an array and not an object with a 'patients' or 'data' array,
        // and it's not an error, assume it's a single patient or empty.
        list = json ? [json] : [];
      }

      const mappedPatients = list.map((patient: any) => ({
        ...patient,
        name: `${patient.firstname || ""} ${patient.lastname || ""}`.trim(),
        status: patient.status || "Active",
      }));
      setPatients(mappedPatients);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (clinicId == null) return;
    setIsLoading(true);
    fetchPatients();
  }, [clinicId, router]);

  // Filter patients by date, search query, and status
  useEffect(() => {
    let result = patients;

    // Filter by date if provided
    if (date) {
      const selectedDate = new Date(date);
      result = result.filter((patient) => {
        const patientDate = new Date(patient.createdAt || patient.visitDate || "");
        return patientDate && isSameDay(patientDate, selectedDate);
      });
    }

    // Filter by search query
    // result = result.filter((patient) => {
    //   const name = `${patient.firstname || ""} ${patient.lastname || ""}`.toLowerCase();
    //   const id = patient.id?.toString().toLowerCase() || "";
    //   const crmId = patient.crmId?.toLowerCase() || "";
    //   return (
    //     name.includes(searchQuery.toLowerCase()) ||
    //     id.includes(searchQuery.toLowerCase()) ||
    //     crmId.includes(searchQuery.toLowerCase())
    //   );
    // });

// ——— after ———
const q = searchQuery.trim().toLowerCase();
result = result.filter((patient) => {
  const name = `${patient.firstname||""} ${patient.lastname||""}`.toLowerCase();
  const id   = patient.id?.toString().toLowerCase() || "";
  const crm  = patient.crmId?.toLowerCase() || "";
  return (
    name.includes(q) ||
    id.includes(q)   ||
    crm.includes(q)
  );
});



    // Filter by status
    if (filterStatus !== "all") {
      result = result.filter((patient) => patient.status === filterStatus);
    }

    setFilteredPatients(isReversed ? [...result].reverse() : result);
    setCurrentPage(1); // Reset to first page when filters change
  }, [patients, date, searchQuery, filterStatus, isReversed]);

  useEffect(() => {
    return () => {
      dispatch(clearPreviousVisitLoadedImagesCompleteData());
      dispatch(clearPreviousVisitLoadedXraysList());
      dispatch(resetPreviousVisitClicked());
    };
  }, [dispatch]);

  const handlePatientClick = (patientId: number | undefined) => {
    if (!patientId) return;
    dispatch(setSelectedPatient(patientId.toString()));
    router.push(`/patients/${patientId}`);
  };

  const handleSortToggle = () => {
    setIsReversed(!isReversed);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const indexOfLastPatient = currentPage * patientsPerPage;
  const indexOfFirstPatient = indexOfLastPatient - patientsPerPage;
  const currentPatients = filteredPatients.slice(indexOfFirstPatient, indexOfLastPatient);
  const totalPages = Math.ceil(filteredPatients.length / patientsPerPage);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground mb-2">Patient Management</h2>
          <p className="text-muted-foreground">
            {date ? `Patients for ${format(new Date(date), "MMM d, yyyy")}` : "View and manage all patient records"}
          </p>
        </div>
        <Button
          onClick={() => !isPastDate && router.push("/patient-form")}
          disabled={isPastDate}
          className={isPastDate ? "opacity-50 cursor-not-allowed" : ""}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Patient
        </Button>
      </div>

      <Card className="bg-card border border-border shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl text-foreground">
              Patients ({filteredPatients.length})
            </CardTitle>
            <div className="flex items-center gap-4">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search patients..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => {
                    dispatch(setSearchQuery(e.target.value));
                    setCurrentPage(1);
                  }}
                />
              </div>
              <Button
                variant="outline"
                size="default"
                className="min-h-[44px]"
                onClick={handleSortToggle}
              >
                <ArrowUpDown className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <Table>
              <TableHeader className="sticky top-0 bg-muted z-10">
                <TableRow>
                  <TableHead className="font-semibold text-foreground">Name</TableHead>
                  <TableHead className="font-semibold text-foreground">CRM ID</TableHead>
                  <TableHead className="font-semibold text-foreground">Last Visit</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                      Loading patient data...
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-8">
                      <div className="text-lg text-destructive mb-2">Error Loading Patients</div>
                      <div className="text-muted-foreground">{error}</div>
                    </TableCell>
                  </TableRow>
                ) : currentPatients.length > 0 ? (
                  currentPatients.map((patient, index) => (
                    <TableRow
                      key={patient.id || index}
                      className={`hover:bg-accent transition-colors cursor-pointer ${
                        index % 2 === 0 ? "bg-card" : "bg-muted/50"
                      }`}
                      // onClick={() => handlePatientClick(patient.id)}
                      onClick={() => {
                        handlePatientClick(patient.id);
                        dispatch(addPatientName(`${patient.firstname || ""} ${patient.lastname || ""}`));
                      }}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                            <span className="text-primary font-semibold text-sm">
                              {patient.firstname?.[0]?.toUpperCase() || "P"}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-foreground">
                              {`${patient.firstname || ""} ${patient.lastname || ""}`.trim() || "No Name"}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {patient.crmId || "N/A"}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {patient.createdAt
                          ? format(new Date(patient.createdAt), "MMM d, yyyy")
                          : "N/A"}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                      {date
                        ? `No patients found for ${format(new Date(date), "MMM d, yyyy")}`
                        : "No patients found"}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </ScrollArea>

          {filteredPatients.length > 0 && (
            <div className="border-t border-border p-4 flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                Showing {Math.min(indexOfFirstPatient + 1, filteredPatients.length)}-
                {Math.min(indexOfLastPatient, filteredPatients.length)} of{" "}
                {filteredPatients.length} patients
              </div>
              <div className="flex items-center gap-1">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                    {totalPages > 0 &&
                      Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => handlePageChange(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() =>
                          currentPage < totalPages && handlePageChange(currentPage + 1)
                        }
                        className={
                          currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"
                        }
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
