"use client"

import type React from "react"
import { useState, useRef, useC<PERSON>back, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Pen,
  Square,
  Circle,
  Eraser,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Move,
  Save,
  Download,
  Undo,
  Redo,
  X,
} from "lucide-react"
import type { Annotation } from "../types"

interface VGGAnnotatorProps {
  imageUrl: string
  annotations?: Annotation[]
  onAnnotationsChange?: (annotations: Annotation[]) => void
  onSave?: (annotations: Annotation[]) => void
  onClose?: () => void
  slotInfo?: {
    type: string
    position: string
    number?: number
  }
}

type DrawingTool = "pen" | "rectangle" | "circle" | "eraser" | "move" | null
type AnnotationType = "decay" | "filling" | "crown" | "implant" | "other"

export function VGGAnnotator({
  imageUrl,
  annotations = [],
  onAnnotationsChange,
  onSave,
  onClose,
  slotInfo,
}: VGGAnnotatorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [currentTool, setCurrentTool] = useState<DrawingTool>("pen")
  const [currentAnnotationType, setCurrentAnnotationType] = useState<AnnotationType>("decay")
  const [isDrawing, setIsDrawing] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 })
  const [currentAnnotations, setCurrentAnnotations] = useState<Annotation[]>(annotations)
  const [history, setHistory] = useState<Annotation[][]>([annotations])
  const [historyIndex, setHistoryIndex] = useState(0)
// ← INSERT: Add brightness & contrast state
const [brightness, setBrightness] = useState<number>(100)
const [contrast, setContrast] = useState<number>(100)



  const annotationColors = {
    decay: "#FF0000",
    filling: "#00FF00",
    crown: "#0000FF",
    implant: "#FF00FF",
    other: "#FFFF00",
  }

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const img = new Image()
    img.crossOrigin = "anonymous"
    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      redrawCanvas()
    }
    img.src = imageUrl
  }, [imageUrl])

  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Apply transformations
    ctx.save()
    ctx.filter = `brightness(${brightness}%) contrast(${contrast}%)`
    ctx.translate(canvas.width / 2 + panOffset.x, canvas.height / 2 + panOffset.y)
    ctx.rotate((rotation * Math.PI) / 180)
    ctx.scale(zoom, zoom)
    ctx.translate(-canvas.width / 2, -canvas.height / 2)

    // Draw image
    const img = new Image()
    img.crossOrigin = "anonymous"
    img.onload = () => {
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

      // Draw annotations
      currentAnnotations.forEach((annotation) => {
        ctx.strokeStyle = annotation.color
        ctx.fillStyle = annotation.color + "40"
        ctx.lineWidth = 2

        if (annotation.type === "rectangle" && annotation.coordinates) {
          const { x, y, width = 50, height = 50 } = annotation.coordinates
          ctx.strokeRect(x, y, width, height)
          ctx.fillRect(x, y, width, height)
        } else if (annotation.type === "circle" && annotation.coordinates) {
          const { x, y, radius = 25 } = annotation.coordinates
          ctx.beginPath()
          ctx.arc(x, y, radius, 0, 2 * Math.PI)
          ctx.stroke()
          ctx.fill()
        } else if (annotation.type === "freehand" && annotation.points) {
          ctx.beginPath()
          annotation.points.forEach((point, index) => {
            if (index === 0) {
              ctx.moveTo(point.x, point.y)
            } else {
              ctx.lineTo(point.x, point.y)
            }
          })
          ctx.stroke()
        }
      })

      ctx.restore()
    }
    img.src = imageUrl
}, [imageUrl, currentAnnotations, zoom, rotation, panOffset, brightness, contrast])


  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!currentTool) return

      const canvas = canvasRef.current
      if (!canvas) return

      const rect = canvas.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      setIsDrawing(true)

      if (currentTool === "pen") {
        const newAnnotation: Annotation = {
          id: `annotation-${Date.now()}`,
          type: "freehand",
          points: [{ x, y }],
          color: annotationColors[currentAnnotationType],
          label: currentAnnotationType,
        }
        setCurrentAnnotations((prev) => [...prev, newAnnotation])
      } else if (currentTool === "rectangle") {
        const newAnnotation: Annotation = {
          id: `annotation-${Date.now()}`,
          type: "rectangle",
          coordinates: { x, y, width: 0, height: 0 },
          color: annotationColors[currentAnnotationType],
          label: currentAnnotationType,
        }
        setCurrentAnnotations((prev) => [...prev, newAnnotation])
      } else if (currentTool === "circle") {
        const newAnnotation: Annotation = {
          id: `annotation-${Date.now()}`,
          type: "circle",
          coordinates: { x, y, radius: 0 },
          color: annotationColors[currentAnnotationType],
          label: currentAnnotationType,
        }
        setCurrentAnnotations((prev) => [...prev, newAnnotation])
      }
    },
    [currentTool, currentAnnotationType],
  )

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!isDrawing || !currentTool) return

      const canvas = canvasRef.current
      if (!canvas) return

      const rect = canvas.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      setCurrentAnnotations((prev) => {
        const newAnnotations = [...prev]
        const lastAnnotation = newAnnotations[newAnnotations.length - 1]

        if (currentTool === "pen" && lastAnnotation.type === "freehand") {
          lastAnnotation.points = [...(lastAnnotation.points || []), { x, y }]
        } else if (currentTool === "rectangle" && lastAnnotation.type === "rectangle") {
          const startX = lastAnnotation.coordinates?.x || 0
          const startY = lastAnnotation.coordinates?.y || 0
          lastAnnotation.coordinates = {
            x: Math.min(startX, x),
            y: Math.min(startY, y),
            width: Math.abs(x - startX),
            height: Math.abs(y - startY),
          }
        } else if (currentTool === "circle" && lastAnnotation.type === "circle") {
          const startX = lastAnnotation.coordinates?.x || 0
          const startY = lastAnnotation.coordinates?.y || 0
          const radius = Math.sqrt(Math.pow(x - startX, 2) + Math.pow(y - startY, 2))
          lastAnnotation.coordinates = { x: startX, y: startY, radius }
        }

        return newAnnotations
      })
    },
    [isDrawing, currentTool],
  )

  const handleMouseUp = useCallback(() => {
    if (isDrawing) {
      setIsDrawing(false)
      // Add to history
      setHistory((prev) => [...prev.slice(0, historyIndex + 1), currentAnnotations])
      setHistoryIndex((prev) => prev + 1)
      onAnnotationsChange?.(currentAnnotations)
    }
  }, [isDrawing, currentAnnotations, historyIndex, onAnnotationsChange])

  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex((prev) => prev - 1)
      setCurrentAnnotations(history[historyIndex - 1])
    }
  }, [historyIndex, history])

  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex((prev) => prev + 1)
      setCurrentAnnotations(history[historyIndex + 1])
    }
  }, [historyIndex, history])

  const handleSave = useCallback(() => {
    onSave?.(currentAnnotations)
  }, [currentAnnotations, onSave])

  useEffect(() => {
    redrawCanvas()
  }, [redrawCanvas])

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-[95vw] h-[95vh] flex flex-col">
        <CardHeader className="flex-shrink-0 pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-900">VGG Annotator</CardTitle>
              {slotInfo && (
                <p className="text-sm text-gray-600 mt-1">
                  {slotInfo.type} - {slotInfo.position} {slotInfo.number || ""}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex gap-4 overflow-hidden">
          {/* Tools Panel */}
          <div className="w-64 flex-shrink-0 space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Drawing Tools</h3>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant={currentTool === "pen" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentTool("pen")}
                >
                  <Pen className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentTool === "rectangle" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentTool("rectangle")}
                >
                  <Square className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentTool === "circle" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentTool("circle")}
                >
                  <Circle className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentTool === "eraser" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentTool("eraser")}
                >
                  <Eraser className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentTool === "move" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentTool("move")}
                >
                  <Move className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-semibold mb-2">Annotation Type</h3>
              <div className="space-y-2">
                {Object.entries(annotationColors).map(([type, color]) => (
                  <Button
                    key={type}
                    variant={currentAnnotationType === type ? "default" : "outline"}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setCurrentAnnotationType(type as AnnotationType)}
                  >
                    <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: color }} />
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-semibold mb-2">View Controls</h3>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => setZoom((prev) => Math.min(prev + 0.1, 3))}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setZoom((prev) => Math.max(prev - 0.1, 0.1))}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => setRotation((prev) => prev - 90)}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setRotation((prev) => prev + 90)}>
                    <RotateCw className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleUndo} disabled={historyIndex === 0}>
                    <Undo className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRedo}
                    disabled={historyIndex === history.length - 1}
                  >
                    <Redo className="h-4 w-4" />
                  </Button>
                </div>

                {/* ← INSERT: Brightness & Contrast sliders go right here */}
<div className="space-y-2">
  {/* Brightness Slider */}
  <div className="flex items-center gap-2">
    <label htmlFor="brightness-range" className="text-sm">Brightness:</label>
    <input
      id="brightness-range"
      type="range"
      min="0"
      max="200"
      value={brightness}
      onChange={(e) => setBrightness(Number(e.target.value))}
      className="flex-1"
    />
    <span className="w-8 text-right text-xs">{brightness}%</span>
  </div>
  {/* Contrast Slider */}
  <div className="flex items-center gap-2">
    <label htmlFor="contrast-range" className="text-sm">Contrast:</label>
    <input
      id="contrast-range"
      type="range"
      min="0"
      max="200"
      value={contrast}
      onChange={(e) => setContrast(Number(e.target.value))}
      className="flex-1"
    />
    <span className="w-8 text-right text-xs">{contrast}%</span>
  </div>
</div>

              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-semibold mb-2">Annotations ({currentAnnotations.length})</h3>
              <ScrollArea className="h-32">
                <div className="space-y-1">
                  {currentAnnotations.map((annotation, index) => (
                    <div key={annotation.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: annotation.color }} />
                        <span className="text-xs">{annotation.label}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => {
                          setCurrentAnnotations((prev) => prev.filter((_, i) => i !== index))
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 flex items-center justify-center bg-gray-100 rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              className="max-w-full max-h-full cursor-crosshair"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
