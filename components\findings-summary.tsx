"use client"

import type { Decay } from "./dental-xray-viewer"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, FileText, Send } from "lucide-react"
import { cn } from "@/lib/utils"

interface FindingsSummaryProps {
  decayFindings: Decay[]
  selectedTooth: number | null
  onSelectTooth: (toothNumber: number | null) => void
}

export default function FindingsSummary({ decayFindings, selectedTooth, onSelectTooth }: FindingsSummaryProps) {
  const filteredFindings = selectedTooth
    ? decayFindings.filter((finding) => finding.toothNumber === selectedTooth)
    : decayFindings

  const severeCases = filteredFindings.filter((finding) => finding.severity === "severe").length
  const moderateCases = filteredFindings.filter((finding) => finding.severity === "moderate").length
  const mildCases = filteredFindings.filter((finding) => finding.severity === "mild").length

  return (
    <div>
      <div className="p-4 border-b border-slate-200 flex items-center justify-between">
        <div>
          <h2 className="font-semibold text-lg text-slate-800">AI Findings Summary</h2>
          <p className="text-sm text-slate-500">{selectedTooth ? `Tooth #${selectedTooth}` : "All teeth"}</p>
        </div>

        <div className="flex items-center gap-3">
          <Badge variant="destructive" className={`rounded-xl ${severeCases === 0 ? "opacity-50" : ""}`}>
            {severeCases} Severe
          </Badge>
          <Badge variant="default" className={`bg-amber-500 rounded-xl ${moderateCases === 0 ? "opacity-50" : ""}`}>
            {moderateCases} Moderate
          </Badge>
          <Badge
            variant="outline"
            className={`text-yellow-700 border-yellow-300 rounded-xl ${mildCases === 0 ? "opacity-50" : ""}`}
          >
            {mildCases} Mild
          </Badge>
        </div>
      </div>

      <div className="p-4">
        {filteredFindings.length === 0 ? (
          <div className="text-center py-8 text-slate-500">
            <AlertCircle className="mx-auto mb-2 text-slate-400" />
            <p>No findings for the selected tooth. Please select another tooth.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredFindings.map((finding) => (
              <div
                key={finding.id}
                className={cn(
                  "border rounded-lg p-4 transition-all",
                  finding.severity === "severe" && "border-red-200 bg-red-50",
                  finding.severity === "moderate" && "border-amber-200 bg-amber-50",
                  finding.severity === "mild" && "border-yellow-100 bg-yellow-50",
                )}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-medium">
                      Tooth #{finding.toothNumber} - {finding.location}
                    </h3>
                    <Badge
                      variant={
                        finding.severity === "severe"
                          ? "destructive"
                          : finding.severity === "moderate"
                            ? "default"
                            : "outline"
                      }
                      className={cn(
                        "mt-1",
                        finding.severity === "moderate" && "bg-amber-500",
                        finding.severity === "mild" && "text-yellow-700 border-yellow-300",
                      )}
                    >
                      {finding.severity.charAt(0).toUpperCase() + finding.severity.slice(1)}
                    </Badge>
                  </div>
                  <Button variant="ghost" size="sm" className="h-7" onClick={() => onSelectTooth(finding.toothNumber)}>
                    View
                  </Button>
                </div>

                <p className="text-sm mt-2">{finding.description}</p>

                <div className="mt-3 pt-3 border-t">
                  <h4 className="text-sm font-medium mb-1">Recommendations:</h4>
                  <p className="text-sm">{finding.recommendations}</p>
                </div>

                <div className="flex gap-2 mt-3">
                  <Button variant="secondary" size="sm" className="text-xs h-7 gap-1">
                    <FileText size={12} />
                    Add to report
                  </Button>
                  <Button variant="secondary" size="sm" className="text-xs h-7 gap-1">
                    <Send size={12} />
                    Share with patient
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
