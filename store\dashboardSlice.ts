// src/store/dashboardSlice.ts

import { createSlice, PayloadAction } from "@reduxjs/toolkit";


interface DashboardState {
   // … other state …
   calendarDate: string | null;
   isCalendarOpen: boolean;
}


const initialState: DashboardState = {
   // … 
   calendarDate: null,
   isCalendarOpen: false,
};



const dashboardSlice = createSlice({
 name: "dashboard",
  initialState,
  reducers: {
    setCalendarOpen(state, action: PayloadAction<boolean>) {
      state.isCalendarOpen = action.payload;
    },
    setCalendarDate(state, action: PayloadAction<string | null>) {
      state.calendarDate = action.payload;
    },
  },
});


export const { setCalendarOpen, setCalendarDate } = dashboardSlice.actions;
export default dashboardSlice.reducer;
