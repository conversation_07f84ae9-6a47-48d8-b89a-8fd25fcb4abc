import { createSlice, PayloadAction } from "@reduxjs/toolkit";
 
 
export interface ImageResult {
    url: string;
    type: string;
}
 
interface Procedure {
    code: string
    name: string
    toothNumber?: string
  }
 
interface Visit {
    visitId: string
    visitDate: string
    dentistName: string
    createdAt: string
    procedures: Procedure[]
    imageCount: number
  patientData?: { id: string; [key: string]: any };
  }
 
export interface PreviousVisitData {
    patientId: number;
    visitId: number;
    total: number;
    results: ImageResult[];
}
 
 
export interface ImageAnalysisResult {
    id: string;
    url: string;
    type: string;
    date: string;       // ISO string (you can also use `Date` if you plan to parse it)
    analyzed: boolean;
    position?: string;
    imageId:number
  }
 
interface PreviousVisitState {
    visitId: number | null
    previousPatientVisits: Visit[] | null
    previousVisitloadedCompleteData: PreviousVisitData | null
    previousVisitLoadedXraysList: ImageAnalysisResult[] | null
    previousVisitClicked: boolean
}
 
   const initialState: PreviousVisitState = {
       visitId: null,
       previousPatientVisits: [], // Initialize as an empty array
       previousVisitloadedCompleteData: null,
       previousVisitLoadedXraysList: null,
       previousVisitClicked: false,
   };
   
 
const previousVisitSlice = createSlice({
    name: "previousVisit",
   initialState,
    reducers: {
        addPatientVisitId: (state, action: PayloadAction<number | null>) => {
            state.visitId = action.payload
        },
        removePatientVisitId: (state) => {
            state.visitId = null
        },
        addPreviousVisitLoadedImagesCompleteData: (state, action: PayloadAction<PreviousVisitData | null>) => {
            state.previousVisitloadedCompleteData = action.payload;
        },
        clearPreviousVisitLoadedImagesCompleteData: (state) => {
            state.previousVisitloadedCompleteData = null;
        },
        addPreviousVisitLoadedXraysList: (state, action: PayloadAction<ImageAnalysisResult[] | null>) => {
            state.previousVisitLoadedXraysList = action.payload ? [...action.payload] : null;
        },
        clearPreviousVisitLoadedXraysList: (state) => {
            state.previousVisitLoadedXraysList = null;
        },
        togglePreviousVisitClicked: (state) => {
            state.previousVisitClicked = true;
        },
        resetPreviousVisitClicked: (state) => {
            state.previousVisitClicked = false;
        },
        addPreviousPatientsVisits: (state, action: PayloadAction<Visit[] | null>) => {
            state.previousPatientVisits = action.payload ? [...action.payload] : null;
        },
        resetPreviousPatientsVisits: (state) => {
            state.previousPatientVisits = null;
        },
        updateVisitImageCount: (state, action: PayloadAction<{visitId: string, newCount: number}>) => {
  state.previousPatientVisits = state.previousPatientVisits?.map(v =>
    v.visitId === action.payload.visitId
      ? {...v, imageCount: action.payload.newCount}
      : v
  ) ?? [];
}

    }
})
 
 
export const { addPatientVisitId, removePatientVisitId, addPreviousVisitLoadedImagesCompleteData,
    clearPreviousVisitLoadedImagesCompleteData,
    addPreviousVisitLoadedXraysList, clearPreviousVisitLoadedXraysList,
    togglePreviousVisitClicked,
    resetPreviousVisitClicked,
    addPreviousPatientsVisits,
    resetPreviousPatientsVisits,
 updateVisitImageCount
} = previousVisitSlice.actions;
 
export default previousVisitSlice.reducer;
 