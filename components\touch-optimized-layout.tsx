"use client"

import type React from "react"

import { TopNavigation } from "./top-navigation"
import { usePathname } from "next/navigation"

interface TouchOptimizedLayoutProps {
  children: React.ReactNode
}

export default function TouchOptimizedLayout({ children }: TouchOptimizedLayoutProps) {
  const pathname = usePathname()
  const isLogin = pathname === "/login"

  return (
      <div className="min-h-screen bg-background">

     { !isLogin && <TopNavigation /> }

     { isLogin
       // full-bleed for login
       ? <>{children}</>
       // normal container for all other pages
       : <main className="container mx-auto px-4 py-1">{children}</main>
     }
    </div>
  )
}
