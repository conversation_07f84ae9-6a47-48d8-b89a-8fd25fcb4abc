"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Download, Share2, ZoomIn, ZoomOut, Move } from "lucide-react"
import ToothNotationPanel from "./tooth-notation-panel"
import FindingsSummary from "./findings-summary"
import ComparisonTimeline from "./comparison-timeline"
import XrayViewer from "./xray-viewer"

export interface Decay {
  id: string
  toothNumber: number
  severity: "severe" | "moderate" | "mild" | "none"
  location: string
  description: string
  recommendations: string
}

export default function DentalXrayViewer() {
  const [showAI, setShowAI] = useState(true)
  const [zoom, setZoom] = useState(100)
  const [previousXrays, setPreviousXrays] = useState([
    { id: "xray-1", date: "2023-05-15", label: "May 15, 2023" },
    { id: "xray-2", date: "2022-11-22", label: "November 22, 2022" },
    { id: "xray-3", date: "2022-05-10", label: "May 10, 2022" },
  ])
  const [currentXrayId, setCurrentXrayId] = useState("current")
  const [isPanActive, setIsPanActive] = useState(false)
  const [selectedTooth, setSelectedTooth] = useState<number | null>(null)

  // Sample decay findings
  const [decayFindings, setDecayFindings] = useState<Decay[]>([
    {
      id: "decay-1",
      toothNumber: 18,
      severity: "severe",
      location: "Distal surface",
      description: "Deep decay extending to the pulp",
      recommendations: "Root canal treatment followed by crown",
    },
    {
      id: "decay-2",
      toothNumber: 14,
      severity: "moderate",
      location: "Occlusal surface",
      description: "Moderate decay in the enamel and dentin",
      recommendations: "Composite restoration",
    },
    {
      id: "decay-3",
      toothNumber: 31,
      severity: "mild",
      location: "Mesial surface",
      description: "Early enamel decay",
      recommendations: "Fluoride treatment and monitoring",
    },
    {
      id: "decay-4",
      toothNumber: 3,
      severity: "moderate",
      location: "Buccal surface",
      description: "Decay along the gumline",
      recommendations: "Composite restoration",
    },
  ])

  const handleZoomIn = () => {
    if (zoom < 200) setZoom(zoom + 10)
  }

  const handleZoomOut = () => {
    if (zoom > 50) setZoom(zoom - 10)
  }

  const handleExport = () => {
    alert("Exporting findings and images...")
    // In a real app, this would generate a PDF or similar document
  }

  const handleShare = () => {
    alert("Opening share dialog...")
    // In a real app, this would open a sharing modal
  }

  return (
    <div className="flex flex-col h-screen max-h-screen">
      {/* Header */}
      <header className="px-4 py-3 border-b border-slate-200 bg-white flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold text-slate-800">Dental Decay Identification</h1>
          <p className="text-sm text-slate-500">Patient: Sarah Johnson • DOB: 07/15/1985 • ID: SJ12345</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center space-x-2">
            <Switch id="ai-toggle" checked={showAI} onCheckedChange={setShowAI} />
            <Label htmlFor="ai-toggle" className="font-medium text-sm">
              AI Annotations
            </Label>
          </div>
          <Button variant="outline" size="sm" onClick={handleExport} className="gap-1">
            <Download size={16} />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare} className="gap-1">
            <Share2 size={16} />
            Share
          </Button>
        </div>
      </header>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Main viewer */}
        <div className="flex-1 p-4 flex flex-col h-full overflow-hidden">
          <div className="relative flex-1 border rounded-lg overflow-hidden bg-slate-50 mb-3">
            <XrayViewer
              showAI={showAI}
              zoom={zoom}
              isPanActive={isPanActive}
              selectedTooth={selectedTooth}
              currentXrayId={currentXrayId}
              decayFindings={decayFindings}
            />

            {/* Controls overlay */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white/90 backdrop-blur-sm p-2 rounded-lg shadow-sm border">
              <Button variant="outline" size="icon" onClick={handleZoomOut} disabled={zoom <= 50}>
                <ZoomOut size={18} />
              </Button>
              <Slider
                value={[zoom]}
                min={50}
                max={200}
                step={5}
                onValueChange={(value) => setZoom(value[0])}
                className="w-32"
              />
              <Button variant="outline" size="icon" onClick={handleZoomIn} disabled={zoom >= 200}>
                <ZoomIn size={18} />
              </Button>
              <div className="w-px h-6 bg-slate-200 mx-1" />
              <Button
                variant={isPanActive ? "secondary" : "outline"}
                size="icon"
                onClick={() => setIsPanActive(!isPanActive)}
                title="Pan tool"
              >
                <Move size={18} />
              </Button>
            </div>
          </div>

          {/* Timeline controls */}
          <Card className="mb-3">
            <CardContent className="py-3">
              <ComparisonTimeline
                xrays={[{ id: "current", date: "2024-01-10", label: "Current (Jan 10, 2024)" }, ...previousXrays]}
                currentXrayId={currentXrayId}
                onSelectXray={setCurrentXrayId}
              />
            </CardContent>
          </Card>

          {/* Findings summary */}
          <Card className="overflow-hidden">
            <FindingsSummary
              decayFindings={decayFindings}
              selectedTooth={selectedTooth}
              onSelectTooth={setSelectedTooth}
            />
          </Card>
        </div>

        {/* Side panel */}
        <div className="w-72 border-l border-slate-200 bg-white overflow-y-auto">
          <ToothNotationPanel
            decayFindings={decayFindings}
            selectedTooth={selectedTooth}
            onSelectTooth={setSelectedTooth}
          />
        </div>
      </div>
    </div>
  )
}
