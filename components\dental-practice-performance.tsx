"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts"
import {
  Download,
  Users,
  Activity,
  Banknote,
  Sparkles,
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Filter,
  BarChart2,
  Info,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Tooltip as TooltipComponent, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function DentalPracticePerformance() {
  const [dateRange, setDateRange] = useState("month")
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [endDate, setEndDate] = useState<Date | undefined>(new Date())
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const [activeChart, setActiveChart] = useState("patientFlow")

  // Sample data
  const kpiData = {
    newPatients: { value: 42, change: 8.5, trend: "up" },
    procedures: { value: 327, change: 15.2, trend: "up" },
    revenue: { value: 87450, change: 12.3, trend: "up" },
    aiDiagnoses: { value: 215, change: 42.8, trend: "up" },
  }

  const patientFlowData = [
    { date: "Jan 1", patients: 24 },
    { date: "Jan 2", patients: 18 },
    { date: "Jan 3", patients: 30 },
    { date: "Jan 4", patients: 26 },
    { date: "Jan 5", patients: 32 },
    { date: "Jan 6", patients: 28 },
    { date: "Jan 7", patients: 12 },
    { date: "Jan 8", patients: 27 },
    { date: "Jan 9", patients: 31 },
    { date: "Jan 10", patients: 29 },
    { date: "Jan 11", patients: 33 },
    { date: "Jan 12", patients: 25 },
    { date: "Jan 13", patients: 21 },
    { date: "Jan 14", patients: 15 },
  ]

  const treatmentAcceptanceData = [
    { name: "Fillings", aiRecommended: 78, accepted: 65 },
    { name: "Root Canals", aiRecommended: 42, accepted: 36 },
    { name: "Crowns", aiRecommended: 53, accepted: 48 },
    { name: "Cleanings", aiRecommended: 110, accepted: 102 },
    { name: "Extractions", aiRecommended: 38, accepted: 31 },
  ]

  const aiInsightsData = [
    { month: "Aug", detectionRate: 62 },
    { month: "Sep", detectionRate: 68 },
    { month: "Oct", detectionRate: 72 },
    { month: "Nov", detectionRate: 78 },
    { month: "Dec", detectionRate: 85 },
    { month: "Jan", detectionRate: 91 },
  ]

  const satisfactionData = [
    { name: "Very Satisfied", value: 68 },
    { name: "Satisfied", value: 22 },
    { name: "Neutral", value: 7 },
    { name: "Dissatisfied", value: 3 },
  ]

  const revenueByServiceData = [
    { name: "Preventative", value: 32500 },
    { name: "Restorative", value: 28750 },
    { name: "Cosmetic", value: 15200 },
    { name: "Orthodontic", value: 11000 },
  ]

  const patientDemographicsData = [
    { name: "0-18", value: 15 },
    { name: "19-35", value: 28 },
    { name: "36-50", value: 32 },
    { name: "51-65", value: 18 },
    { name: "65+", value: 7 },
  ]

  const COLORS = ["#4ade80", "#a3e635", "#fcd34d", "#f87171"]
  const DEMOGRAPHICS_COLORS = ["#60a5fa", "#818cf8", "#a78bfa", "#c084fc", "#e879f9"]

  const handleExportReport = () => {
    alert("Exporting report...")
  }

  const formatDate = (date: Date | undefined) => {
    if (!date) return ""
    return format(date, "MMM d, yyyy")
  }

  const getDateRangeText = () => {
    if (!date) return "Select date range"
    if (!endDate || date === endDate) return formatDate(date)
    return `${formatDate(date)} - ${formatDate(endDate)}`
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="px-6 py-4 border-b border-slate-200 bg-white flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-slate-800">Practice Performance Dashboard</h1>
          <p className="text-sm text-slate-500">Sunrise Dental Clinic • Last updated: Jan 15, 2024 at 9:30 AM</p>
        </div>
        <div className="flex items-center gap-3">
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-1 min-w-[200px] justify-start">
                <Calendar size={16} />
                <span>{getDateRangeText()}</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <div className="p-3 border-b">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Select date range</h3>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn("text-xs h-7", dateRange === "week" && "bg-slate-100")}
                      onClick={() => setDateRange("week")}
                    >
                      Week
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn("text-xs h-7", dateRange === "month" && "bg-slate-100")}
                      onClick={() => setDateRange("month")}
                    >
                      Month
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn("text-xs h-7", dateRange === "quarter" && "bg-slate-100")}
                      onClick={() => setDateRange("quarter")}
                    >
                      Quarter
                    </Button>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 p-3">
                <div>
                  <p className="text-xs font-medium mb-2">Start date</p>
                  <CalendarComponent
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    className="rounded border"
                    disabled={(date) => date > new Date()}
                  />
                </div>
                <div>
                  <p className="text-xs font-medium mb-2">End date</p>
                  <CalendarComponent
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    className="rounded border"
                    disabled={(date) => date > new Date() || (!!date && date < date)}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2 p-3 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setDate(new Date())
                    setEndDate(new Date())
                    setIsCalendarOpen(false)
                  }}
                >
                  Today
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    setIsCalendarOpen(false)
                  }}
                >
                  Apply
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-1">
                <Filter size={16} />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>All Procedures</DropdownMenuItem>
              <DropdownMenuItem>Preventative</DropdownMenuItem>
              <DropdownMenuItem>Restorative</DropdownMenuItem>
              <DropdownMenuItem>Cosmetic</DropdownMenuItem>
              <DropdownMenuItem>Orthodontic</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button onClick={handleExportReport} className="gap-1">
            <Download size={16} />
            Export Report
          </Button>
        </div>
      </header>

      {/* Main content */}
      <div className="p-6">
        {/* KPIs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <Users className="text-blue-600" size={24} />
                </div>
                {kpiData.newPatients.trend === "up" ? (
                  <Badge className="bg-green-500">
                    <TrendingUp size={14} className="mr-1" />
                    {kpiData.newPatients.change}%
                  </Badge>
                ) : (
                  <Badge variant="destructive">
                    <TrendingDown size={14} className="mr-1" />
                    {kpiData.newPatients.change}%
                  </Badge>
                )}
              </div>
              <h3 className="text-3xl font-bold">{kpiData.newPatients.value}</h3>
              <p className="text-sm text-slate-500">New Patients</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center">
                  <Activity className="text-indigo-600" size={24} />
                </div>
                {kpiData.procedures.trend === "up" ? (
                  <Badge className="bg-green-500">
                    <TrendingUp size={14} className="mr-1" />
                    {kpiData.procedures.change}%
                  </Badge>
                ) : (
                  <Badge variant="destructive">
                    <TrendingDown size={14} className="mr-1" />
                    {kpiData.procedures.change}%
                  </Badge>
                )}
              </div>
              <h3 className="text-3xl font-bold">{kpiData.procedures.value}</h3>
              <p className="text-sm text-slate-500">Procedures Performed</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center">
                  <Banknote className="text-emerald-600" size={24} />
                </div>
                {kpiData.revenue.trend === "up" ? (
                  <Badge className="bg-green-500">
                    <TrendingUp size={14} className="mr-1" />
                    {kpiData.revenue.change}%
                  </Badge>
                ) : (
                  <Badge variant="destructive">
                    <TrendingDown size={14} className="mr-1" />
                    {kpiData.revenue.change}%
                  </Badge>
                )}
              </div>
              <h3 className="text-3xl font-bold">${kpiData.revenue.value.toLocaleString()}</h3>
              <p className="text-sm text-slate-500">Revenue</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-violet-100 flex items-center justify-center">
                  <Sparkles className="text-violet-600" size={24} />
                </div>
                {kpiData.aiDiagnoses.trend === "up" ? (
                  <Badge className="bg-green-500">
                    <TrendingUp size={14} className="mr-1" />
                    {kpiData.aiDiagnoses.change}%
                  </Badge>
                ) : (
                  <Badge variant="destructive">
                    <TrendingDown size={14} className="mr-1" />
                    {kpiData.aiDiagnoses.change}%
                  </Badge>
                )}
              </div>
              <h3 className="text-3xl font-bold">{kpiData.aiDiagnoses.value}</h3>
              <p className="text-sm text-slate-500">AI-Assisted Diagnoses</p>
            </CardContent>
          </Card>
        </div>

        {/* Chart selection tabs */}
        <div className="mb-6">
          <Tabs defaultValue="patientFlow" value={activeChart} onValueChange={setActiveChart}>
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="patientFlow" className="gap-2">
                  <BarChart2 size={16} />
                  Patient Flow
                </TabsTrigger>
                <TabsTrigger value="treatmentAcceptance" className="gap-2">
                  <Activity size={16} />
                  Treatment Acceptance
                </TabsTrigger>
                <TabsTrigger value="aiInsights" className="gap-2">
                  <Sparkles size={16} />
                  AI Insights
                </TabsTrigger>
                <TabsTrigger value="revenueAnalysis" className="gap-2">
                  <Banknote size={16} />
                  Revenue Analysis
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <Select defaultValue="day">
                  <SelectTrigger className="w-[120px] h-8 text-xs">
                    <SelectValue placeholder="View by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="day">Daily</SelectItem>
                    <SelectItem value="week">Weekly</SelectItem>
                    <SelectItem value="month">Monthly</SelectItem>
                  </SelectContent>
                </Select>

                <TooltipProvider>
                  <TooltipComponent>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <Download size={16} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Export Chart</p>
                    </TooltipContent>
                  </TooltipComponent>
                </TooltipProvider>
              </div>
            </div>

            <TabsContent value="patientFlow" className="mt-0">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Patient Flow Trends</CardTitle>
                      <CardDescription>Daily patient visits over time</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={patientFlowData} margin={{ top: 20, right: 20, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id="colorPatients" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Area
                          type="monotone"
                          dataKey="patients"
                          stroke="#3b82f6"
                          fillOpacity={1}
                          fill="url(#colorPatients)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="treatmentAcceptance" className="mt-0">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Treatment Acceptance Rates</CardTitle>
                      <CardDescription>AI-recommended vs. actually accepted treatments</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={treatmentAcceptanceData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="aiRecommended" fill="#8884d8" name="AI Recommended" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="accepted" fill="#82ca9d" name="Actually Accepted" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="aiInsights" className="mt-0">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>AI Diagnostic Improvements</CardTitle>
                      <CardDescription>Detection rate improvements over time</CardDescription>
                    </div>
                    <Badge className="bg-blue-500">+29% in 6 months</Badge>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={aiInsightsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="month" />
                        <YAxis domain={[50, 100]} />
                        <Tooltip />
                        <Line
                          type="monotone"
                          dataKey="detectionRate"
                          name="Detection Rate (%)"
                          stroke="#8884d8"
                          strokeWidth={2}
                          dot={{ r: 4 }}
                          activeDot={{ r: 6 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="revenueAnalysis" className="mt-0">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Revenue by Service Type</CardTitle>
                      <CardDescription>Distribution of revenue across service categories</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={revenueByServiceData}
                          cx="50%"
                          cy="50%"
                          labelLine={true}
                          outerRadius={120}
                          fill="#8884d8"
                          dataKey="value"
                          nameKey="name"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {revenueByServiceData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`$${value}`, "Revenue"]} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Patient Satisfaction */}
          <Card className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Patient Satisfaction</CardTitle>
                  <CardDescription>Overall satisfaction ratings from patient surveys</CardDescription>
                </div>
                <Badge className="bg-green-500">
                  <TrendingUp size={14} className="mr-1" />
                  5.2%
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={satisfactionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {satisfactionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Patient Demographics */}
          <Card className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Patient Demographics</CardTitle>
                  <CardDescription>Age distribution of patient population</CardDescription>
                </div>
                <TooltipProvider>
                  <TooltipComponent>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Info size={16} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Click on segments to drill down into specific age groups</p>
                    </TooltipContent>
                  </TooltipComponent>
                </TooltipProvider>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={patientDemographicsData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {patientDemographicsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={DEMOGRAPHICS_COLORS[index % DEMOGRAPHICS_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Alerts and insights */}
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Insights and Alerts</CardTitle>
              <CardDescription>Important metrics and recommendations based on your practice data</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg bg-amber-50 border-amber-200">
                  <div className="flex gap-3">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="text-amber-500" />
                    </div>
                    <div>
                      <h4 className="font-medium text-amber-800">Appointment Cancellation Rate Alert</h4>
                      <p className="text-sm text-amber-700 mt-1">
                        Cancellation rate for wisdom tooth extractions has increased to 18% (4% above benchmark).
                        Consider implementing confirmation reminders.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex gap-3">
                    <div className="flex-shrink-0">
                      <Sparkles className="text-blue-500" />
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-800">AI Detection Improvement</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        The AI-assisted cavity detection system has shown a 42.8% improvement in early-stage detection
                        compared to traditional methods.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-green-50 border-green-200">
                  <div className="flex gap-3">
                    <div className="flex-shrink-0">
                      <TrendingUp className="text-green-500" />
                    </div>
                    <div>
                      <h4 className="font-medium text-green-800">Treatment Plan Optimization</h4>
                      <p className="text-sm text-green-700 mt-1">
                        AI-recommended treatment sequencing has reduced total treatment time by an average of 22% while
                        maintaining the same quality outcomes.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-red-50 border-red-200">
                  <div className="flex gap-3">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="text-red-500" />
                    </div>
                    <div>
                      <h4 className="font-medium text-red-800">Inventory Alert</h4>
                      <p className="text-sm text-red-700 mt-1">
                        Composite filling material inventory is below threshold (15%). Please reorder within the next 7
                        days to avoid scheduling disruptions.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
