{"name": "my-v0-project", "version": "1.0.9", "private": true, "scripts": {"prebuild": "npm version patch --no-git-tag-version && genversion --es6 --force lib/autobuild_version.js", "build": "next build", "dev": "next dev", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reduxjs/toolkit": "latest", "@use-gesture/react": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "date-fns-tz": "^3.2.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-redux": "latest", "react-resizable-panels": "^2.1.7", "react-toastify": "^11.0.5", "recharts": "latest", "redux": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/jwt-decode": "^2.2.1", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "genversion": "^3.2.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}