"use client"

import { useState } from "react"
import type { Decay } from "./dental-xray-viewer"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

interface ToothNotationPanelProps {
  decayFindings: Decay[]
  selectedTooth: number | null
  onSelectTooth: (toothNumber: number | null) => void
}

export default function ToothNotationPanel({ decayFindings, selectedTooth, onSelectTooth }: ToothNotationPanelProps) {
  const [activeTab, setActiveTab] = useState("universal")

  // Helper function to get tooth status
  const getToothStatus = (toothNumber: number) => {
    const finding = decayFindings.find((d) => d.toothNumber === toothNumber)
    return finding ? finding.severity : "none"
  }

  // Universal notation system teeth
  const upperTeeth = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
  const lowerTeeth = [32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17]

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-slate-200">
        <h2 className="font-semibold text-lg text-slate-800">Tooth Notation</h2>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="universal">Universal</TabsTrigger>
            <TabsTrigger value="palmer">Palmer</TabsTrigger>
          </TabsList>

          <TabsContent value="universal" className="mt-0">
            <div className="mb-8">
              <p className="text-xs text-slate-500 mb-2">Upper Teeth</p>
              <div className="grid grid-cols-8 gap-1">
                {upperTeeth.map((toothNumber) => (
                  <button
                    key={toothNumber}
                    className={cn(
                      "aspect-square p-1 rounded border text-xs font-medium flex flex-col items-center justify-center",
                      selectedTooth === toothNumber
                        ? "border-blue-500 bg-blue-50"
                        : "border-slate-200 hover:bg-slate-50",
                      getToothStatus(toothNumber) === "severe" && "ring-2 ring-red-500 ring-offset-1",
                      getToothStatus(toothNumber) === "moderate" && "ring-2 ring-amber-500 ring-offset-1",
                      getToothStatus(toothNumber) === "mild" && "ring-2 ring-yellow-300 ring-offset-1",
                    )}
                    onClick={() => onSelectTooth(selectedTooth === toothNumber ? null : toothNumber)}
                  >
                    <span>{toothNumber}</span>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <p className="text-xs text-slate-500 mb-2">Lower Teeth</p>
              <div className="grid grid-cols-8 gap-1">
                {lowerTeeth.map((toothNumber) => (
                  <button
                    key={toothNumber}
                    className={cn(
                      "aspect-square p-1 rounded border text-xs font-medium flex flex-col items-center justify-center",
                      selectedTooth === toothNumber
                        ? "border-blue-500 bg-blue-50"
                        : "border-slate-200 hover:bg-slate-50",
                      getToothStatus(toothNumber) === "severe" && "ring-2 ring-red-500 ring-offset-1",
                      getToothStatus(toothNumber) === "moderate" && "ring-2 ring-amber-500 ring-offset-1",
                      getToothStatus(toothNumber) === "mild" && "ring-2 ring-yellow-300 ring-offset-1",
                    )}
                    onClick={() => onSelectTooth(selectedTooth === toothNumber ? null : toothNumber)}
                  >
                    <span>{toothNumber}</span>
                  </button>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="palmer" className="mt-0">
            <div className="flex items-center justify-center h-32">
              <p className="text-sm text-slate-500">Palmer notation system will be displayed here</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <div className="p-4 flex-1 overflow-y-auto">
        {/* Severity Legend */}
        <div className="mt-8 pt-6 border-t border-slate-100">
          <h3 className="font-medium text-sm mb-3">Severity Legend</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
              <span className="text-sm">Severe decay</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-amber-500 mr-2"></div>
              <span className="text-sm">Moderate decay</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-yellow-300 mr-2"></div>
              <span className="text-sm">Mild decay</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
