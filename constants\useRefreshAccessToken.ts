'use client';
import { clearTokens, setAccessToken } from '@/store/userSlice';
import { REFRESH_TOKEN_URL } from './apiRoutes';
import { store } from '@/store/store';
import { setRefreshToken } from '@/store/userSlice';
 
 
const fetchWithRefresh = async (url: string, options: RequestInit = {}, router: any) => {
 
    const state = store.getState();
    const token = state.user.accessToken;
 
    // Initial fetch request
    let response = await fetch(url, {
        ...options,
        headers: {
            ...options.headers,
            // Remove 'Content-Type': 'application/json' since FormData in handleFileUpload requires multipart/form-data
            Authorization: `Bearer ${token}`,
        },
        credentials: 'include',
    });
 
    // If unauthorized, try refresh
    if (response.status === 401) {
        console.warn('🔒 Token expired, trying to refresh...');
 
        const refreshRes = await fetch(REFRESH_TOKEN_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
        });
 
        if (refreshRes.status === 200) {
            console.log('✅ Token refreshed. Retrying original request...');
            const result = await refreshRes.json(); // Parse the refresh response
            console.log("Result in fetchWithRefresh", result, result?.data?.accessToken);
 
            // Dispatch the new access token to the Redux store using store.dispatch
            store.dispatch(setAccessToken(result?.data?.accessToken));
            store.dispatch(setRefreshToken(result?.data?.refreshToken))
 
            // Retry the original request with the new token
            response = await fetch(url, {
                ...options,
                headers: {
                    ...options.headers,
                    Authorization: `Bearer ${result?.data?.accessToken}`,
                },
                credentials: 'include',
            });
        } else {
            console.error('❌ Refresh token failed, redirecting to login...');
            router.push('/login');
            store.dispatch(clearTokens());
            return null; // Return null to indicate failure
        }
    }
 
    return response;
};
 
export default fetchWithRefresh;
