"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { ChevronLeft, ChevronRight, Clock } from "lucide-react"

interface TimelineProps {
  xrays: Array<{
    id: string
    date: string
    label: string
  }>
  currentXrayId: string
  onSelectXray: (id: string) => void
}

export default function ComparisonTimeline({ xrays, currentXrayId, onSelectXray }: TimelineProps) {
  const currentIndex = xrays.findIndex((x) => x.id === currentXrayId)

  const handlePrevious = () => {
    if (currentIndex < xrays.length - 1) {
      onSelectXray(xrays[currentIndex + 1].id)
    }
  }

  const handleNext = () => {
    if (currentIndex > 0) {
      onSelectXray(xrays[currentIndex - 1].id)
    }
  }

  const handleSliderChange = (value: number[]) => {
    // Convert slider value (0-100) to index in xrays array
    // const index = Math.floor((100 - value[0]) / (100 / (xrays.length - 1)))
    const index = Math.floor((100 - value[0]) / stepSize)
    onSelectXray(xrays[index].id)
  }


  // Calculate slider value based on current index
  // const sliderValue = 100 - currentIndex * (100 / (xrays.length - 1))
  // Avoid division by zero when there's only one xray:
 const maxSteps = xrays.length > 1 ? xrays.length - 1 : 1
 const stepSize = 100 / maxSteps
 // Calculate slider value based on current index
 const sliderValue = 100 - currentIndex * stepSize

  return (
    <div className="flex items-center gap-4">
      <Button
        variant="outline"
        size="sm"
        className="h-8 w-8 p-0"
        onClick={handlePrevious}
        disabled={currentIndex >= xrays.length - 1}
      >
        <ChevronLeft size={16} />
        <span className="sr-only">Previous X-ray</span>
      </Button>

      <div className="flex-1 flex items-center gap-3">
        <Clock size={16} className="text-slate-400" />
        <div className="flex-1">
          <Slider
            value={[sliderValue]}
            min={0}
            max={100}
            // step={100 / (xrays.length - 1)}
            step={stepSize}
            onValueChange={handleSliderChange}
          />
        </div>
      </div>

      <Button variant="outline" size="sm" className="h-8 w-8 p-0" onClick={handleNext} disabled={currentIndex <= 0}>
        <ChevronRight size={16} />
        <span className="sr-only">Next X-ray</span>
      </Button>

      <div className="min-w-32 text-right text-sm font-medium">{xrays[currentIndex].label}</div>
    </div>
  )
}
