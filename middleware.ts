// middleware.ts
import { NextResponse, NextRequest } from 'next/server'

// export function middleware(req: NextRequest) {


//   const url = req.nextUrl.clone()
//   const { pathname, searchParams } = req.nextUrl

//   // 1) Public assets, _next internals, API routes always pass
//   if (
//     pathname.startsWith('/_next') ||
//     pathname.startsWith('/static') ||
//     pathname.startsWith('/api')
//   ) {
//     return NextResponse.next()
//   }

//   // 2) If we explicitly came here after logout (e.g. /login?loggedout=1),
//   //    clear your cookies just that one time
//   if (
//     pathname === '/login' &&
//     searchParams.has('loggedout')
//   ) {
//     const res = NextResponse.next()

//     // delete your JS cookies
//     res.cookies.delete('auth-session')
//     res.cookies.delete('email')

//     // delete the names your backend set (underscores)
//     res.cookies.delete('access_token')
//     res.cookies.delete('refresh_token')
//     return res
//   }

//   // 3) Allow the login page itself (normal visits)
//   if (pathname === '/login') {
//     return NextResponse.next()
//   }

//   // 4) Otherwise block if no real access_token present
//   const access = req.cookies.get('access_token')?.value
//   if (access) {
//     return NextResponse.next()
//   }

//   // 5) If you’re not logged in, bounce to login
//   url.pathname = '/login'
//   return NextResponse.redirect(url)
// }

// export const config = {
//   matcher: ['/:path*'],
// }





// middleware.ts
export function middleware(req: NextRequest) {
  // allow everything through
  return NextResponse.next()
}

export const config = { matcher: [] }
