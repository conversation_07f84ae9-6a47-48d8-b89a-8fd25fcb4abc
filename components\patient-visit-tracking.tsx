"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import {
  Search,
  Filter,
  Bell,
  Clock,
  FileText,
  SmileIcon as Tooth,
  UserPlus,
  Calendar,
  MoreHorizontal,
  FileSpreadsheet,
  MessageSquare,
  AlertCircle,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd"
import { cn } from "@/lib/utils"

interface Patient {
  id: string
  avatar: string
  name: string
  time: string
  duration: number
  procedure: string
  dentist: string
  status: "waiting" | "in-progress" | "completed" | "follow-up"
  waitTime?: number
  aiFindings?: {
    count: number
    severity: "low" | "medium" | "high"
  }
}

export default function PatientVisitTracking() {
  const [patients, setPatients] = useState<Patient[]>([
    {
      id: "p1",
      avatar: "/placeholder.svg?key=3vxb2",
      name: "<PERSON>",
      time: "09:00 AM",
      duration: 60,
      procedure: "Root Canal",
      dentist: "Dr. Williams",
      status: "in-progress",
      waitTime: 0,
      aiFindings: {
        count: 2,
        severity: "high",
      },
    },
    {
      id: "p2",
      avatar: "/placeholder.svg?key=hhwdd",
      name: "James Smith",
      time: "09:30 AM",
      duration: 45,
      procedure: "Cleaning",
      dentist: "Dr. Martinez",
      status: "waiting",
      waitTime: 15,
    },
    {
      id: "p3",
      avatar: "/placeholder.svg?key=06if9",
      name: "Emily Thompson",
      time: "10:00 AM",
      duration: 30,
      procedure: "Examination",
      dentist: "Dr. Williams",
      status: "waiting",
      waitTime: 45,
      aiFindings: {
        count: 1,
        severity: "medium",
      },
    },
    {
      id: "p4",
      avatar: "/placeholder.svg?key=7j4rf",
      name: "Michael Garcia",
      time: "10:15 AM",
      duration: 90,
      procedure: "Crown Fitting",
      dentist: "Dr. Roberts",
      status: "waiting",
      waitTime: 60,
    },
    {
      id: "p5",
      avatar: "/placeholder.svg?key=zv6zb",
      name: "Robert Chen",
      time: "08:45 AM",
      duration: 30,
      procedure: "Filling",
      dentist: "Dr. Martinez",
      status: "completed",
    },
    {
      id: "p6",
      avatar: "/placeholder.svg?key=w4ut1",
      name: "Tyler Wilson",
      time: "11:30 AM",
      duration: 45,
      procedure: "Orthodontic Adjustment",
      dentist: "Dr. Roberts",
      status: "follow-up",
      aiFindings: {
        count: 3,
        severity: "medium",
      },
    },
    {
      id: "p7",
      avatar: "/older-man-contemplative.png",
      name: "David Johnson",
      time: "08:30 AM",
      duration: 60,
      procedure: "Extraction",
      dentist: "Dr. Williams",
      status: "completed",
    },
    {
      id: "p8",
      avatar: "/middle-aged-woman.png",
      name: "Maria Sanchez",
      time: "12:00 PM",
      duration: 30,
      procedure: "Consultation",
      dentist: "Dr. Martinez",
      status: "follow-up",
    },
  ])

  // Handle drag and drop
  const onDragEnd = (result: any) => {
    const { destination, source, draggableId } = result

    // If there's no destination or the destination is the same as the source
    if (!destination || (destination.droppableId === source.droppableId && destination.index === source.index)) {
      return
    }

    const patient = patients.find((p) => p.id === draggableId)
    if (!patient) return

    // Create a new array of patients with the patient removed
    const newPatients = patients.filter((p) => p.id !== draggableId)

    // Update the status of the patient
    const updatedPatient = {
      ...patient,
      status: destination.droppableId as "waiting" | "in-progress" | "completed" | "follow-up",
    }

    // Insert the patient at the new index
    newPatients.splice(destination.index, 0, updatedPatient)

    // Update the state
    setPatients(newPatients)
  }

  // Filter patients by status
  const waitingPatients = patients.filter((p) => p.status === "waiting")
  const inProgressPatients = patients.filter((p) => p.status === "in-progress")
  const completedPatients = patients.filter((p) => p.status === "completed")
  const followUpPatients = patients.filter((p) => p.status === "follow-up")

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="px-6 py-4 border-b border-slate-200 bg-white flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-slate-800">Patient Visit Tracking</h1>
          <p className="text-sm text-slate-500">Today: January 15, 2024</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
            <Input type="search" placeholder="Search patients..." className="w-[260px] pl-8" />
          </div>
          <Button variant="outline" className="gap-1">
            <Filter size={16} />
            Filter
          </Button>
          <Button variant="outline" className="gap-1">
            <Calendar size={16} />
            Today
          </Button>
          <Button className="gap-1">
            <UserPlus size={16} />
            Add Patient
          </Button>
        </div>
      </header>

      {/* Main content */}
      <div className="p-6">
        <Tabs defaultValue="kanban">
          <div className="flex justify-between items-center mb-6">
            <TabsList>
              <TabsTrigger value="kanban" className="gap-2">
                <FileSpreadsheet size={16} />
                Kanban Board
              </TabsTrigger>
              <TabsTrigger value="calendar" className="gap-2">
                <Calendar size={16} />
                Calendar View
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              <Badge variant="outline" className="gap-1 border-amber-200 text-amber-700 bg-amber-50 hover:bg-amber-100">
                <Clock size={12} />
                Average Wait: 32 min
              </Badge>
              <Button variant="outline" size="icon" className="relative">
                <Bell size={16} />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </Button>
            </div>
          </div>

          <TabsContent value="kanban" className="mt-0">
            <DragDropContext onDragEnd={onDragEnd}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Waiting Column */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-amber-50 pb-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center gap-2 text-amber-700">
                        <div className="relative">
                          <Clock size={18} className="text-amber-500" />
                          {waitingPatients.length > 0 && (
                            <span className="absolute -top-1 -right-1 bg-amber-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                              {waitingPatients.length}
                            </span>
                          )}
                        </div>
                        Checked In
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <Droppable droppableId="waiting">
                    {(provided:any) => (
                      <CardContent
                        className="p-3 min-h-[calc(100vh-14rem)]"
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                      >
                        {waitingPatients.length === 0 ? (
                          <div className="h-20 border border-dashed rounded-lg flex items-center justify-center text-slate-400 text-sm">
                            No patients waiting
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {waitingPatients.map((patient, index) => (
                              <Draggable key={patient.id} draggableId={patient.id} index={index}>
                                {(provided:any) => (
                                  <Card
                                    className="overflow-hidden"
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                  >
                                    <CardContent className="p-3 space-y-3">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Avatar>
                                            <AvatarImage
                                              src={patient.avatar || "/placeholder.svg"}
                                              alt={patient.name}
                                            />
                                            <AvatarFallback>{patient.name.charAt(0)}</AvatarFallback>
                                          </Avatar>
                                          <div>
                                            <p className="font-medium leading-none">{patient.name}</p>
                                            <p className="text-xs text-slate-500 mt-1">
                                              {patient.time} • {patient.duration} min
                                            </p>
                                          </div>
                                        </div>
                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                          <MoreHorizontal size={16} />
                                        </Button>
                                      </div>

                                      <div className="flex flex-wrap gap-2">
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.procedure}
                                        </Badge>
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.dentist}
                                        </Badge>
                                      </div>

                                      <div className="flex justify-between items-center pt-2 border-t border-slate-100">
                                        <Badge
                                          variant="outline"
                                          className={cn(
                                            "gap-1 text-xs font-normal",
                                            patient.waitTime && patient.waitTime > 30
                                              ? "border-red-200 text-red-700 bg-red-50"
                                              : "border-slate-200 text-slate-700 bg-slate-50",
                                          )}
                                        >
                                          <Clock size={10} />
                                          Wait: {patient.waitTime} min
                                        </Badge>

                                        <div className="flex gap-1">
                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                            <Tooth size={16} />
                                          </Button>
                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                            <FileText size={16} />
                                          </Button>
                                          {patient.aiFindings && (
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className={cn(
                                                "h-8 w-8 relative",
                                                patient.aiFindings.severity === "high"
                                                  ? "text-red-500"
                                                  : patient.aiFindings.severity === "medium"
                                                    ? "text-amber-500"
                                                    : "text-blue-500",
                                              )}
                                            >
                                              <AlertCircle size={16} />
                                              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                                                {patient.aiFindings.count}
                                              </span>
                                            </Button>
                                          )}
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                )}
                              </Draggable>
                            ))}
                          </div>
                        )}
                        {provided.placeholder}
                      </CardContent>
                    )}
                  </Droppable>
                </Card>

                {/* In Progress Column */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-blue-50 pb-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center gap-2 text-blue-700">
                        <div className="relative">
                          <Tooth size={18} className="text-blue-500" />
                          {inProgressPatients.length > 0 && (
                            <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                              {inProgressPatients.length}
                            </span>
                          )}
                        </div>
                        In Progress
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <Droppable droppableId="in-progress">
                    {(provided:any) => (
                      <CardContent
                        className="p-3 min-h-[calc(100vh-14rem)]"
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                      >
                        {inProgressPatients.length === 0 ? (
                          <div className="h-20 border border-dashed rounded-lg flex items-center justify-center text-slate-400 text-sm">
                            No patients in progress
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {inProgressPatients.map((patient, index) => (
                              <Draggable key={patient.id} draggableId={patient.id} index={index}>
                                {(provided:any) => (
                                  <Card
                                    className="overflow-hidden"
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                  >
                                    <CardContent className="p-3 space-y-3">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Avatar>
                                            <AvatarImage
                                              src={patient.avatar || "/placeholder.svg"}
                                              alt={patient.name}
                                            />
                                            <AvatarFallback>{patient.name.charAt(0)}</AvatarFallback>
                                          </Avatar>
                                          <div>
                                            <p className="font-medium leading-none">{patient.name}</p>
                                            <p className="text-xs text-slate-500 mt-1">
                                              {patient.time} • {patient.duration} min
                                            </p>
                                          </div>
                                        </div>
                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                          <MoreHorizontal size={16} />
                                        </Button>
                                      </div>

                                      <div className="flex flex-wrap gap-2">
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.procedure}
                                        </Badge>
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.dentist}
                                        </Badge>
                                      </div>

                                      <div className="flex justify-between items-center pt-2 border-t border-slate-100">
                                        <Badge
                                          variant="outline"
                                          className="gap-1 text-xs font-normal border-blue-200 text-blue-700 bg-blue-50"
                                        >
                                          <Clock size={10} />
                                          In Room: 15 min
                                        </Badge>

                                        <div className="flex gap-1">
                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                            <Tooth size={16} />
                                          </Button>
                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                            <FileText size={16} />
                                          </Button>
                                          {patient.aiFindings && (
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className={cn(
                                                "h-8 w-8 relative",
                                                patient.aiFindings.severity === "high"
                                                  ? "text-red-500"
                                                  : patient.aiFindings.severity === "medium"
                                                    ? "text-amber-500"
                                                    : "text-blue-500",
                                              )}
                                            >
                                              <AlertCircle size={16} />
                                              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                                                {patient.aiFindings.count}
                                              </span>
                                            </Button>
                                          )}
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                )}
                              </Draggable>
                            ))}
                          </div>
                        )}
                        {provided.placeholder}
                      </CardContent>
                    )}
                  </Droppable>
                </Card>

                {/* Completed Column */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-green-50 pb-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center gap-2 text-green-700">
                        <div className="relative">
                          <FileText size={18} className="text-green-500" />
                          {completedPatients.length > 0 && (
                            <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                              {completedPatients.length}
                            </span>
                          )}
                        </div>
                        Completed
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <Droppable droppableId="completed">
                    {(provided:any) => (
                      <CardContent
                        className="p-3 min-h-[calc(100vh-14rem)]"
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                      >
                        {completedPatients.length === 0 ? (
                          <div className="h-20 border border-dashed rounded-lg flex items-center justify-center text-slate-400 text-sm">
                            No completed patients
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {completedPatients.map((patient, index) => (
                              <Draggable key={patient.id} draggableId={patient.id} index={index}>
                                {(provided:any) => (
                                  <Card
                                    className="overflow-hidden"
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                  >
                                    <CardContent className="p-3 space-y-3">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Avatar>
                                            <AvatarImage
                                              src={patient.avatar || "/placeholder.svg"}
                                              alt={patient.name}
                                            />
                                            <AvatarFallback>{patient.name.charAt(0)}</AvatarFallback>
                                          </Avatar>
                                          <div>
                                            <p className="font-medium leading-none">{patient.name}</p>
                                            <p className="text-xs text-slate-500 mt-1">
                                              {patient.time} • {patient.duration} min
                                            </p>
                                          </div>
                                        </div>
                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                          <MoreHorizontal size={16} />
                                        </Button>
                                      </div>

                                      <div className="flex flex-wrap gap-2">
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.procedure}
                                        </Badge>
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.dentist}
                                        </Badge>
                                      </div>

                                      <div className="flex justify-between items-center pt-2 border-t border-slate-100">
                                        <Button variant="outline" size="sm" className="h-7 text-xs gap-1">
                                          <MessageSquare size={12} />
                                          Send Follow-up
                                        </Button>

                                        <div className="flex gap-1">
                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                            <FileText size={16} />
                                          </Button>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                )}
                              </Draggable>
                            ))}
                          </div>
                        )}
                        {provided.placeholder}
                      </CardContent>
                    )}
                  </Droppable>
                </Card>

                {/* Follow-up Column */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-purple-50 pb-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center gap-2 text-purple-700">
                        <div className="relative">
                          <MessageSquare size={18} className="text-purple-500" />
                          {followUpPatients.length > 0 && (
                            <span className="absolute -top-1 -right-1 bg-purple-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                              {followUpPatients.length}
                            </span>
                          )}
                        </div>
                        Follow-up
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <Droppable droppableId="follow-up">
                    {(provided:any) => (
                      <CardContent
                        className="p-3 min-h-[calc(100vh-14rem)]"
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                      >
                        {followUpPatients.length === 0 ? (
                          <div className="h-20 border border-dashed rounded-lg flex items-center justify-center text-slate-400 text-sm">
                            No follow-up patients
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {followUpPatients.map((patient, index) => (
                              <Draggable key={patient.id} draggableId={patient.id} index={index}>
                                {(provided:any) => (
                                  <Card
                                    className="overflow-hidden"
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                  >
                                    <CardContent className="p-3 space-y-3">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Avatar>
                                            <AvatarImage
                                              src={patient.avatar || "/placeholder.svg"}
                                              alt={patient.name}
                                            />
                                            <AvatarFallback>{patient.name.charAt(0)}</AvatarFallback>
                                          </Avatar>
                                          <div>
                                            <p className="font-medium leading-none">{patient.name}</p>
                                            <p className="text-xs text-slate-500 mt-1">
                                              {patient.time} • {patient.duration} min
                                            </p>
                                          </div>
                                        </div>
                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                          <MoreHorizontal size={16} />
                                        </Button>
                                      </div>

                                      <div className="flex flex-wrap gap-2">
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.procedure}
                                        </Badge>
                                        <Badge variant="outline" className="text-xs font-normal">
                                          {patient.dentist}
                                        </Badge>
                                      </div>

                                      <div className="flex justify-between items-center pt-2 border-t border-slate-100">
                                        <Button variant="outline" size="sm" className="h-7 text-xs gap-1">
                                          <Calendar size={12} />
                                          Schedule Next
                                        </Button>

                                        <div className="flex gap-1">
                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                            <FileText size={16} />
                                          </Button>
                                          {patient.aiFindings && (
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className={cn(
                                                "h-8 w-8 relative",
                                                patient.aiFindings.severity === "high"
                                                  ? "text-red-500"
                                                  : patient.aiFindings.severity === "medium"
                                                    ? "text-amber-500"
                                                    : "text-blue-500",
                                              )}
                                            >
                                              <AlertCircle size={16} />
                                              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                                                {patient.aiFindings.count}
                                              </span>
                                            </Button>
                                          )}
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                )}
                              </Draggable>
                            ))}
                          </div>
                        )}
                        {provided.placeholder}
                      </CardContent>
                    )}
                  </Droppable>
                </Card>
              </div>
            </DragDropContext>
          </TabsContent>

          <TabsContent value="calendar" className="mt-0">
            <div className="border rounded-lg p-8 flex items-center justify-center h-[calc(100vh-14rem)]">
              <div className="text-center">
                <Calendar size={48} className="mx-auto mb-4 text-slate-400" />
                <h3 className="text-lg font-medium mb-2">Calendar View</h3>
                <p className="text-slate-500 max-w-md">
                  The calendar view allows you to see all appointments scheduled for the day, week, or month.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
