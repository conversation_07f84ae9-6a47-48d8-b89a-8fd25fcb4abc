"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Download,
  Check,
  X,
  Edit2,
  AlertTriangle,
  Info,
  Trash2,
  Printer,
  Share2,
  Clock,
  DollarSign,
  SmileIcon as Tooth,
} from "lucide-react"
import TreatmentTimeline from "./treatment-timeline"
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface Treatment {
  id: string
  name: string
  teeth: number[]
  estimatedDuration: number // in minutes
  costRangeLow: number
  costRangeHigh: number
  priority: "immediate" | "short-term" | "preventative"
  rationale: string
  approved: boolean | null
  aiConfidence: number // 0-100
  notes?: string
  alternativeOptions?: string[]
}

export default function TreatmentPlanningDashboard() {
  const [detailedView, setDetailedView] = useState(false)
  const [treatments, setTreatments] = useState<Treatment[]>([
    {
      id: "t1",
      name: "Root Canal Therapy",
      teeth: [18],
      estimatedDuration: 90,
      costRangeLow: 800,
      costRangeHigh: 1200,
      priority: "immediate",
      rationale:
        "Deep decay extending to the pulp with periapical radiolucency visible on X-ray. Patient reports intermittent pain in the area.",
      approved: null,
      aiConfidence: 92,
      alternativeOptions: ["Extraction"],
    },
    {
      id: "t2",
      name: "Composite Restoration",
      teeth: [14, 15],
      estimatedDuration: 60,
      costRangeLow: 200,
      costRangeHigh: 350,
      priority: "short-term",
      rationale: "Moderate decay in the enamel and dentin on occlusal surfaces. No pulpal involvement detected.",
      approved: null,
      aiConfidence: 88,
    },
    {
      id: "t3",
      name: "Crown Placement",
      teeth: [18],
      estimatedDuration: 60,
      costRangeLow: 900,
      costRangeHigh: 1300,
      priority: "short-term",
      rationale:
        "Following root canal treatment, crown is necessary to protect the tooth structure and prevent fracture.",
      approved: null,
      aiConfidence: 95,
      alternativeOptions: ["Onlay Restoration"],
    },
    {
      id: "t4",
      name: "Fluoride Treatment",
      teeth: [31, 32],
      estimatedDuration: 15,
      costRangeLow: 30,
      costRangeHigh: 60,
      priority: "preventative",
      rationale:
        "Early enamel demineralization detected. Professional fluoride application to remineralize and prevent further decay.",
      approved: null,
      aiConfidence: 78,
    },
    {
      id: "t5",
      name: "Scaling and Root Planing",
      teeth: [17, 18, 19],
      estimatedDuration: 45,
      costRangeLow: 200,
      costRangeHigh: 400,
      priority: "short-term",
      rationale:
        "Moderate periodontal pockets (4-5mm) with bleeding on probing. Localized gingival inflammation observed.",
      approved: null,
      aiConfidence: 85,
    },
    {
      id: "t6",
      name: "Dental Sealants",
      teeth: [2, 3, 14, 15],
      estimatedDuration: 30,
      costRangeLow: 120,
      costRangeHigh: 200,
      priority: "preventative",
      rationale:
        "Deep pits and fissures on molars with no current decay. Preventative measure to reduce risk of future caries.",
      approved: null,
      aiConfidence: 82,
    },
  ])

  const [selectedTreatment, setSelectedTreatment] = useState<Treatment | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editedTreatment, setEditedTreatment] = useState<Treatment | null>(null)
  const [activeTab, setActiveTab] = useState("immediate")
  const [xrayZoom, setXrayZoom] = useState(100)
  const [showAIAnnotations, setShowAIAnnotations] = useState(true)
  const [patientInfo, setPatientInfo] = useState({
    name: "Sarah Johnson",
    dob: "07/15/1985",
    id: "SJ12345",
    lastVisit: "11/22/2023",
    allergies: ["Penicillin", "Latex"],
    medicalNotes: "Patient has controlled hypertension",
  })

  const immediateCount = treatments.filter((t) => t.priority === "immediate").length
  const shortTermCount = treatments.filter((t) => t.priority === "short-term").length
  const preventativeCount = treatments.filter((t) => t.priority === "preventative").length

  const approvedCount = treatments.filter((t) => t.approved === true).length
  const rejectedCount = treatments.filter((t) => t.approved === false).length
  const pendingCount = treatments.filter((t) => t.approved === null).length

  const handleApproveTreatment = (id: string, approved: boolean) => {
    setTreatments(treatments.map((t) => (t.id === id ? { ...t, approved } : t)))
  }

  const handleEditTreatment = (treatment: Treatment) => {
    setEditedTreatment({ ...treatment })
    setIsEditDialogOpen(true)
  }

  const handleSaveEdit = () => {
    if (editedTreatment) {
      setTreatments(treatments.map((t) => (t.id === editedTreatment.id ? editedTreatment : t)))
      setIsEditDialogOpen(false)
      setEditedTreatment(null)
    }
  }

  const handleDeleteTreatment = (id: string) => {
    setTreatments(treatments.filter((t) => t.id !== id))
  }

  const handleExportPlan = () => {
    alert("Exporting treatment plan...")
  }

  const handlePrintPlan = () => {
    alert("Printing treatment plan...")
  }

  const handleSharePlan = () => {
    alert("Opening share dialog...")
  }

  // Handle drag and drop reordering
  const onDragEnd = (result: any) => {
    const { destination, source, draggableId } = result

    if (!destination) return

    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return
    }

    // If moving between priority categories
    if (destination.droppableId !== source.droppableId) {
      const treatment = treatments.find((t) => t.id === draggableId)
      if (!treatment) return

      // Update the treatment's priority
      const updatedTreatments = treatments.map((t) => {
        if (t.id === draggableId) {
          return {
            ...t,
            priority: destination.droppableId as "immediate" | "short-term" | "preventative",
          }
        }
        return t
      })

      setTreatments(updatedTreatments)
      return
    }

    // Reordering within the same priority category
    const reorder = (list: Treatment[], startIndex: number, endIndex: number) => {
      const result = Array.from(list)
      const [removed] = result.splice(startIndex, 1)
      result.splice(endIndex, 0, removed)
      return result
    }

    const filteredTreatments = treatments.filter((t) => t.priority === source.droppableId)
    const reordered = reorder(filteredTreatments, source.index, destination.index)

    // Merge back with the treatments that weren't affected
    const otherTreatments = treatments.filter((t) => t.priority !== source.droppableId)
    setTreatments([...otherTreatments, ...reordered])
  }

  return (
    <div className="flex flex-col h-screen max-h-screen">
      {/* Header */}
      <header className="px-4 py-3 border-b border-slate-200 bg-white dark:bg-slate-900 dark:border-slate-700 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src="/diverse-person-portrait.png" alt={patientInfo.name} />
            <AvatarFallback>{patientInfo.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-xl font-semibold text-slate-800 dark:text-slate-100">AI Treatment Planning</h1>
            <p className="text-sm text-slate-500 dark:text-slate-400">
              Patient: {patientInfo.name} • DOB: {patientInfo.dob} • ID: {patientInfo.id}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center space-x-2">
            <Switch id="ai-annotations" checked={showAIAnnotations} onCheckedChange={setShowAIAnnotations} />
            <Label htmlFor="ai-annotations" className="font-medium text-sm dark:text-slate-300">
              AI Annotations
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch id="detailed-view" checked={detailedView} onCheckedChange={setDetailedView} />
            <Label htmlFor="detailed-view" className="font-medium text-sm dark:text-slate-300">
              Detailed View
            </Label>
          </div>
          <Separator orientation="vertical" className="h-6" />
          <div className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handlePrintPlan}>
                    <Printer size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Print Plan</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleSharePlan}>
                    <Share2 size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Share Plan</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button onClick={handleExportPlan} className="gap-1">
              <Download size={16} />
              Export Plan
            </Button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Dental scan view */}
        <div className="w-1/2 p-4 border-r border-slate-200 dark:border-slate-700 flex flex-col h-full overflow-hidden">
          <Card className="flex-1 overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Dental Scan</CardTitle>
                <div className="flex items-center gap-2">
                  <Slider
                    value={[xrayZoom]}
                    min={50}
                    max={200}
                    step={5}
                    onValueChange={(value) => setXrayZoom(value[0])}
                    className="w-32"
                  />
                  <span className="text-xs text-slate-500 w-10 text-right">{xrayZoom}%</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 overflow-hidden relative">
              <div
                className="aspect-video bg-slate-100 dark:bg-slate-800 w-full h-full flex items-center justify-center overflow-hidden"
                style={{
                  position: "relative",
                }}
              >
                <div
                  style={{
                    transform: `scale(${xrayZoom / 100})`,
                    transition: "transform 0.2s ease-out",
                  }}
                >
                  <img src="/panoramic-dental-xray.png" alt="Dental scan" className="max-w-full max-h-full" />

                  {/* AI Annotations */}
                  {showAIAnnotations && (
                    <>
                      {/* Root Canal Area */}
                      <div
                        className="absolute top-[35%] left-[28%] w-12 h-12 rounded-full border-2 border-red-500 animate-pulse"
                        style={{ opacity: 0.7 }}
                      >
                        <div className="absolute -top-8 -left-2 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 text-xs px-2 py-1 rounded whitespace-nowrap">
                          Tooth #18: Severe Decay
                        </div>
                      </div>

                      {/* Composite Restoration Areas */}
                      <div
                        className="absolute top-[42%] left-[35%] w-10 h-10 rounded-full border-2 border-amber-500"
                        style={{ opacity: 0.7 }}
                      >
                        <div className="absolute -top-8 -left-2 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 text-xs px-2 py-1 rounded whitespace-nowrap">
                          Tooth #14: Moderate Decay
                        </div>
                      </div>

                      <div
                        className="absolute top-[42%] left-[38%] w-10 h-10 rounded-full border-2 border-amber-500"
                        style={{ opacity: 0.7 }}
                      >
                        <div className="absolute -bottom-8 -left-2 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 text-xs px-2 py-1 rounded whitespace-nowrap">
                          Tooth #15: Moderate Decay
                        </div>
                      </div>

                      {/* Fluoride Treatment Areas */}
                      <div
                        className="absolute top-[58%] left-[53%] w-8 h-8 rounded-full border-2 border-blue-500"
                        style={{ opacity: 0.7 }}
                      >
                        <div className="absolute -top-8 -left-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded whitespace-nowrap">
                          Tooth #31: Early Demineralization
                        </div>
                      </div>

                      <div
                        className="absolute top-[58%] left-[56%] w-8 h-8 rounded-full border-2 border-blue-500"
                        style={{ opacity: 0.7 }}
                      >
                        <div className="absolute -bottom-8 -left-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded whitespace-nowrap">
                          Tooth #32: Early Demineralization
                        </div>
                      </div>

                      {/* Scaling and Root Planing Areas */}
                      <div
                        className="absolute top-[60%] left-[28%] w-20 h-10 rounded-full border-2 border-purple-500"
                        style={{ opacity: 0.5 }}
                      >
                        <div className="absolute -bottom-8 left-0 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs px-2 py-1 rounded whitespace-nowrap">
                          Teeth #17-19: Periodontal Pockets
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Patient Info Overlay */}
              <div className="absolute bottom-4 left-4 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 max-w-xs">
                <h3 className="text-sm font-medium mb-1 flex items-center gap-1">
                  <Info size={14} />
                  Patient Information
                </h3>
                <div className="text-xs space-y-1 text-slate-700 dark:text-slate-300">
                  <p>
                    <span className="font-medium">Last Visit:</span> {patientInfo.lastVisit}
                  </p>
                  <p>
                    <span className="font-medium">Allergies:</span> {patientInfo.allergies.join(", ")}
                  </p>
                  {detailedView && (
                    <p>
                      <span className="font-medium">Notes:</span> {patientInfo.medicalNotes}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mt-4 overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Treatment Sequence Timeline</CardTitle>
            </CardHeader>
            <CardContent className="py-4">
              <TreatmentTimeline
                treatments={treatments.filter((t) => t.approved !== false)}
                onSelectTreatment={setSelectedTreatment}
                selectedTreatmentId={selectedTreatment?.id}
              />
            </CardContent>
          </Card>
        </div>

        {/* Treatment recommendations */}
        <div className="w-1/2 p-4 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              AI-Generated Treatment Recommendations
            </h2>

            <div className="flex items-center gap-2 text-sm">
              <Badge
                variant="outline"
                className="gap-1 bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800"
              >
                <Check size={12} />
                {approvedCount} Approved
              </Badge>
              <Badge
                variant="outline"
                className="gap-1 bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"
              >
                <X size={12} />
                {rejectedCount} Rejected
              </Badge>
              <Badge
                variant="outline"
                className="gap-1 bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-900/20 dark:text-slate-300 dark:border-slate-800"
              >
                <AlertTriangle size={12} />
                {pendingCount} Pending
              </Badge>
            </div>
          </div>

          <DragDropContext onDragEnd={onDragEnd}>
            <Tabs
              defaultValue="immediate"
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col"
            >
              <TabsList className="grid w-full grid-cols-3 mb-4">
                <TabsTrigger value="immediate" className="relative">
                  Immediate
                  {immediateCount > 0 && (
                    <Badge variant="destructive" className="ml-2 absolute -top-2 -right-2">
                      {immediateCount}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="short-term" className="relative">
                  Short-term
                  {shortTermCount > 0 && (
                    <Badge className="ml-2 absolute -top-2 -right-2 bg-amber-500">{shortTermCount}</Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="preventative" className="relative">
                  Preventative
                  {preventativeCount > 0 && (
                    <Badge variant="outline" className="ml-2 absolute -top-2 -right-2 text-blue-600 border-blue-300">
                      {preventativeCount}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="immediate" className="flex-1 overflow-y-auto space-y-4 mt-0">
                <Droppable droppableId="immediate">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps} className="space-y-4">
                      {treatments
                        .filter((t) => t.priority === "immediate")
                        .map((treatment, index) => (
                          <Draggable key={treatment.id} draggableId={treatment.id} index={index}>
                            {(provided) => (
                              <Card
                                key={treatment.id}
                                className={cn(
                                  "overflow-hidden",
                                  treatment.approved === true && "border-l-4 border-l-green-500",
                                  treatment.approved === false && "border-l-4 border-l-red-500 opacity-60",
                                )}
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                              >
                                <div className="p-4 border-l-4 border-red-500">
                                  <div className="flex justify-between mb-2">
                                    <div>
                                      <h3 className="font-semibold text-base flex items-center gap-2">
                                        {treatment.name}
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <Badge variant="outline" className="ml-2 text-xs font-normal">
                                                {treatment.aiConfidence}% AI Confidence
                                              </Badge>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                              <p>
                                                AI confidence score based on analysis of dental images and patient
                                                history
                                              </p>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      </h3>
                                      <div className="flex items-center gap-2 mt-1 flex-wrap">
                                        <Badge variant="outline" className="gap-1">
                                          <Tooth size={12} />
                                          Teeth: {treatment.teeth.join(", ")}
                                        </Badge>
                                        <Badge variant="outline" className="gap-1">
                                          <Clock size={12} />
                                          {treatment.estimatedDuration} min
                                        </Badge>
                                        <Badge variant="outline" className="gap-1">
                                          <DollarSign size={12} />${treatment.costRangeLow}-${treatment.costRangeHigh}
                                        </Badge>
                                      </div>
                                    </div>
                                    <div className="flex gap-2">
                                      <Button
                                        size="sm"
                                        variant={treatment.approved === false ? "default" : "destructive"}
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleApproveTreatment(treatment.id, false)}
                                        disabled={treatment.approved === false}
                                      >
                                        <X size={16} />
                                        <span className="sr-only">Reject</span>
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant={treatment.approved === true ? "outline" : "default"}
                                        className={cn(
                                          "h-8 w-8 p-0",
                                          treatment.approved === true && "bg-green-500 text-white hover:bg-green-600",
                                        )}
                                        onClick={() => handleApproveTreatment(treatment.id, true)}
                                        disabled={treatment.approved === true}
                                      >
                                        <Check size={16} />
                                        <span className="sr-only">Approve</span>
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleEditTreatment(treatment)}
                                      >
                                        <Edit2 size={16} />
                                        <span className="sr-only">Edit</span>
                                      </Button>
                                    </div>
                                  </div>

                                  {(detailedView || selectedTreatment?.id === treatment.id) && (
                                    <div className="mt-3 pt-3 border-t border-slate-100 dark:border-slate-700">
                                      <h4 className="text-sm font-medium mb-1">Clinical Rationale:</h4>
                                      <p className="text-sm text-slate-600 dark:text-slate-300">
                                        {treatment.rationale}
                                      </p>

                                      {treatment.alternativeOptions && treatment.alternativeOptions.length > 0 && (
                                        <div className="mt-2">
                                          <h4 className="text-sm font-medium mb-1">Alternative Options:</h4>
                                          <ul className="text-sm text-slate-600 dark:text-slate-300 list-disc pl-5">
                                            {treatment.alternativeOptions.map((option, i) => (
                                              <li key={i}>{option}</li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}

                                      {treatment.notes && (
                                        <div className="mt-2">
                                          <h4 className="text-sm font-medium mb-1">Notes:</h4>
                                          <p className="text-sm text-slate-600 dark:text-slate-300">
                                            {treatment.notes}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {!detailedView && selectedTreatment?.id !== treatment.id && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="mt-2 h-7 text-xs"
                                      onClick={() => setSelectedTreatment(treatment)}
                                    >
                                      Show Details
                                    </Button>
                                  )}
                                </div>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                      {provided.placeholder}
                      {treatments.filter((t) => t.priority === "immediate").length === 0 && (
                        <div className="text-center py-8 border border-dashed rounded-lg">
                          <p className="text-slate-500 dark:text-slate-400">No immediate treatments</p>
                          <p className="text-sm text-slate-400 dark:text-slate-500 mt-1">
                            Drag treatments here from other categories
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </TabsContent>

              <TabsContent value="short-term" className="flex-1 overflow-y-auto space-y-4 mt-0">
                <Droppable droppableId="short-term">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps} className="space-y-4">
                      {treatments
                        .filter((t) => t.priority === "short-term")
                        .map((treatment, index) => (
                          <Draggable key={treatment.id} draggableId={treatment.id} index={index}>
                            {(provided) => (
                              <Card
                                key={treatment.id}
                                className={cn(
                                  "overflow-hidden",
                                  treatment.approved === true && "border-l-4 border-l-green-500",
                                  treatment.approved === false && "border-l-4 border-l-red-500 opacity-60",
                                )}
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                              >
                                <div className="p-4 border-l-4 border-amber-500">
                                  <div className="flex justify-between mb-2">
                                    <div>
                                      <h3 className="font-semibold text-base flex items-center gap-2">
                                        {treatment.name}
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <Badge variant="outline" className="ml-2 text-xs font-normal">
                                                {treatment.aiConfidence}% AI Confidence
                                              </Badge>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                              <p>
                                                AI confidence score based on analysis of dental images and patient
                                                history
                                              </p>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      </h3>
                                      <div className="flex items-center gap-2 mt-1 flex-wrap">
                                        <Badge variant="outline" className="gap-1">
                                          <Tooth size={12} />
                                          Teeth: {treatment.teeth.join(", ")}
                                        </Badge>
                                        <Badge variant="outline" className="gap-1">
                                          <Clock size={12} />
                                          {treatment.estimatedDuration} min
                                        </Badge>
                                        <Badge variant="outline" className="gap-1">
                                          <DollarSign size={12} />${treatment.costRangeLow}-${treatment.costRangeHigh}
                                        </Badge>
                                      </div>
                                    </div>
                                    <div className="flex gap-2">
                                      <Button
                                        size="sm"
                                        variant={treatment.approved === false ? "default" : "destructive"}
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleApproveTreatment(treatment.id, false)}
                                        disabled={treatment.approved === false}
                                      >
                                        <X size={16} />
                                        <span className="sr-only">Reject</span>
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant={treatment.approved === true ? "outline" : "default"}
                                        className={cn(
                                          "h-8 w-8 p-0",
                                          treatment.approved === true && "bg-green-500 text-white hover:bg-green-600",
                                        )}
                                        onClick={() => handleApproveTreatment(treatment.id, true)}
                                        disabled={treatment.approved === true}
                                      >
                                        <Check size={16} />
                                        <span className="sr-only">Approve</span>
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleEditTreatment(treatment)}
                                      >
                                        <Edit2 size={16} />
                                        <span className="sr-only">Edit</span>
                                      </Button>
                                    </div>
                                  </div>

                                  {(detailedView || selectedTreatment?.id === treatment.id) && (
                                    <div className="mt-3 pt-3 border-t border-slate-100 dark:border-slate-700">
                                      <h4 className="text-sm font-medium mb-1">Clinical Rationale:</h4>
                                      <p className="text-sm text-slate-600 dark:text-slate-300">
                                        {treatment.rationale}
                                      </p>

                                      {treatment.alternativeOptions && treatment.alternativeOptions.length > 0 && (
                                        <div className="mt-2">
                                          <h4 className="text-sm font-medium mb-1">Alternative Options:</h4>
                                          <ul className="text-sm text-slate-600 dark:text-slate-300 list-disc pl-5">
                                            {treatment.alternativeOptions.map((option, i) => (
                                              <li key={i}>{option}</li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}

                                      {treatment.notes && (
                                        <div className="mt-2">
                                          <h4 className="text-sm font-medium mb-1">Notes:</h4>
                                          <p className="text-sm text-slate-600 dark:text-slate-300">
                                            {treatment.notes}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {!detailedView && selectedTreatment?.id !== treatment.id && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="mt-2 h-7 text-xs"
                                      onClick={() => setSelectedTreatment(treatment)}
                                    >
                                      Show Details
                                    </Button>
                                  )}
                                </div>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                      {provided.placeholder}
                      {treatments.filter((t) => t.priority === "short-term").length === 0 && (
                        <div className="text-center py-8 border border-dashed rounded-lg">
                          <p className="text-slate-500 dark:text-slate-400">No short-term treatments</p>
                          <p className="text-sm text-slate-400 dark:text-slate-500 mt-1">
                            Drag treatments here from other categories
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </TabsContent>

              <TabsContent value="preventative" className="flex-1 overflow-y-auto space-y-4 mt-0">
                <Droppable droppableId="preventative">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps} className="space-y-4">
                      {treatments
                        .filter((t) => t.priority === "preventative")
                        .map((treatment, index) => (
                          <Draggable key={treatment.id} draggableId={treatment.id} index={index}>
                            {(provided) => (
                              <Card
                                key={treatment.id}
                                className={cn(
                                  "overflow-hidden",
                                  treatment.approved === true && "border-l-4 border-l-green-500",
                                  treatment.approved === false && "border-l-4 border-l-red-500 opacity-60",
                                )}
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                              >
                                <div className="p-4 border-l-4 border-blue-500">
                                  <div className="flex justify-between mb-2">
                                    <div>
                                      <h3 className="font-semibold text-base flex items-center gap-2">
                                        {treatment.name}
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <Badge variant="outline" className="ml-2 text-xs font-normal">
                                                {treatment.aiConfidence}% AI Confidence
                                              </Badge>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                              <p>
                                                AI confidence score based on analysis of dental images and patient
                                                history
                                              </p>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      </h3>
                                      <div className="flex items-center gap-2 mt-1 flex-wrap">
                                        <Badge variant="outline" className="gap-1">
                                          <Tooth size={12} />
                                          Teeth: {treatment.teeth.join(", ")}
                                        </Badge>
                                        <Badge variant="outline" className="gap-1">
                                          <Clock size={12} />
                                          {treatment.estimatedDuration} min
                                        </Badge>
                                        <Badge variant="outline" className="gap-1">
                                          <DollarSign size={12} />${treatment.costRangeLow}-${treatment.costRangeHigh}
                                        </Badge>
                                      </div>
                                    </div>
                                    <div className="flex gap-2">
                                      <Button
                                        size="sm"
                                        variant={treatment.approved === false ? "default" : "destructive"}
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleApproveTreatment(treatment.id, false)}
                                        disabled={treatment.approved === false}
                                      >
                                        <X size={16} />
                                        <span className="sr-only">Reject</span>
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant={treatment.approved === true ? "outline" : "default"}
                                        className={cn(
                                          "h-8 w-8 p-0",
                                          treatment.approved === true && "bg-green-500 text-white hover:bg-green-600",
                                        )}
                                        onClick={() => handleApproveTreatment(treatment.id, true)}
                                        disabled={treatment.approved === true}
                                      >
                                        <Check size={16} />
                                        <span className="sr-only">Approve</span>
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="h-8 w-8 p-0"
                                        onClick={() => handleEditTreatment(treatment)}
                                      >
                                        <Edit2 size={16} />
                                        <span className="sr-only">Edit</span>
                                      </Button>
                                    </div>
                                  </div>

                                  {(detailedView || selectedTreatment?.id === treatment.id) && (
                                    <div className="mt-3 pt-3 border-t border-slate-100 dark:border-slate-700">
                                      <h4 className="text-sm font-medium mb-1">Clinical Rationale:</h4>
                                      <p className="text-sm text-slate-600 dark:text-slate-300">
                                        {treatment.rationale}
                                      </p>

                                      {treatment.alternativeOptions && treatment.alternativeOptions.length > 0 && (
                                        <div className="mt-2">
                                          <h4 className="text-sm font-medium mb-1">Alternative Options:</h4>
                                          <ul className="text-sm text-slate-600 dark:text-slate-300 list-disc pl-5">
                                            {treatment.alternativeOptions.map((option, i) => (
                                              <li key={i}>{option}</li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}

                                      {treatment.notes && (
                                        <div className="mt-2">
                                          <h4 className="text-sm font-medium mb-1">Notes:</h4>
                                          <p className="text-sm text-slate-600 dark:text-slate-300">
                                            {treatment.notes}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {!detailedView && selectedTreatment?.id !== treatment.id && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="mt-2 h-7 text-xs"
                                      onClick={() => setSelectedTreatment(treatment)}
                                    >
                                      Show Details
                                    </Button>
                                  )}
                                </div>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                      {provided.placeholder}
                      {treatments.filter((t) => t.priority === "preventative").length === 0 && (
                        <div className="text-center py-8 border border-dashed rounded-lg">
                          <p className="text-slate-500 dark:text-slate-400">No preventative treatments</p>
                          <p className="text-sm text-slate-400 dark:text-slate-500 mt-1">
                            Drag treatments here from other categories
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </TabsContent>
            </Tabs>
          </DragDropContext>
        </div>
      </div>

      {/* Edit Treatment Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Treatment</DialogTitle>
            <DialogDescription>Modify the treatment details below. Click save when you're done.</DialogDescription>
          </DialogHeader>

          {editedTreatment && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-4">
                  <Label htmlFor="treatment-name">Treatment Name</Label>
                  <Input
                    id="treatment-name"
                    value={editedTreatment.name}
                    onChange={(e) => setEditedTreatment({ ...editedTreatment, name: e.target.value })}
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="treatment-teeth">Affected Teeth (comma separated)</Label>
                  <Input
                    id="treatment-teeth"
                    value={editedTreatment.teeth.join(", ")}
                    onChange={(e) =>
                      setEditedTreatment({
                        ...editedTreatment,
                        teeth: e.target.value
                          .split(",")
                          .map((t) => Number.parseInt(t.trim()))
                          .filter((t) => !isNaN(t)),
                      })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="treatment-duration">Duration (min)</Label>
                  <Input
                    id="treatment-duration"
                    type="number"
                    value={editedTreatment.estimatedDuration}
                    onChange={(e) =>
                      setEditedTreatment({
                        ...editedTreatment,
                        estimatedDuration: Number.parseInt(e.target.value) || 0,
                      })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="treatment-priority">Priority</Label>
                  <select
                    id="treatment-priority"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={editedTreatment.priority}
                    onChange={(e) =>
                      setEditedTreatment({
                        ...editedTreatment,
                        priority: e.target.value as "immediate" | "short-term" | "preventative",
                      })
                    }
                  >
                    <option value="immediate">Immediate</option>
                    <option value="short-term">Short-term</option>
                    <option value="preventative">Preventative</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="treatment-cost-low">Cost Range (Low)</Label>
                  <Input
                    id="treatment-cost-low"
                    type="number"
                    value={editedTreatment.costRangeLow}
                    onChange={(e) =>
                      setEditedTreatment({
                        ...editedTreatment,
                        costRangeLow: Number.parseInt(e.target.value) || 0,
                      })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="treatment-cost-high">Cost Range (High)</Label>
                  <Input
                    id="treatment-cost-high"
                    type="number"
                    value={editedTreatment.costRangeHigh}
                    onChange={(e) =>
                      setEditedTreatment({
                        ...editedTreatment,
                        costRangeHigh: Number.parseInt(e.target.value) || 0,
                      })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="treatment-confidence">AI Confidence (%)</Label>
                  <Input
                    id="treatment-confidence"
                    type="number"
                    min="0"
                    max="100"
                    value={editedTreatment.aiConfidence}
                    onChange={(e) =>
                      setEditedTreatment({
                        ...editedTreatment,
                        aiConfidence: Number.parseInt(e.target.value) || 0,
                      })
                    }
                  />
                </div>

                <div className="col-span-4">
                  <Label htmlFor="treatment-rationale">Clinical Rationale</Label>
                  <Textarea
                    id="treatment-rationale"
                    value={editedTreatment.rationale}
                    onChange={(e) => setEditedTreatment({ ...editedTreatment, rationale: e.target.value })}
                    rows={3}
                  />
                </div>

                <div className="col-span-4">
                  <Label htmlFor="treatment-notes">Notes (Optional)</Label>
                  <Textarea
                    id="treatment-notes"
                    value={editedTreatment.notes || ""}
                    onChange={(e) => setEditedTreatment({ ...editedTreatment, notes: e.target.value })}
                    placeholder="Add any additional notes here"
                    rows={2}
                  />
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <Button
              variant="destructive"
              onClick={() => {
                if (editedTreatment) {
                  handleDeleteTreatment(editedTreatment.id)
                  setIsEditDialogOpen(false)
                }
              }}
            >
              <Trash2 size={16} className="mr-2" />
              Delete Treatment
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveEdit}>Save Changes</Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
