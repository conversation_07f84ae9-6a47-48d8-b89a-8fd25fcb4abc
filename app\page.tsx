// // app/page.tsx
// "use client"

// import { useEffect } from "react"
// import { useRouter } from "next/navigation"
// import { useAppSelector } from "@/store/hooks"

// export default function Home() {
//   const router = useRouter()
//   const { roleId, accessToken } = useAppSelector((s) => s.user)

//  useEffect(() => {
//   // still deciding—don’t redirect yet
//   if (!accessToken || roleId === null) return
//  if (!accessToken) {
//       return router.replace("/login");
//     }
//   // now we know both token + roleId
//   if (roleId === 1) {
//     router.replace("/usermanagement")
//   } else {
//     router.replace("/dashboard")
//   }
// }, [accessToken, roleId, router])


//   // nothing to render here
//   return null
// }




// app/page.tsx
// "use client"
// import { useEffect, useState } from "react"
// import { useRouter } from "next/navigation"
// import { useAppSelector } from "@/store/hooks"
// import fetchWithRefresh from "@/constants/useRefreshAccessToken"
// import { REFRESH_TOKEN } from "@/constants/apiRoutes"
// import { log } from "console"

// export default function Home() {
//   const router = useRouter()
//   const { roleId, accessToken } = useAppSelector(s => s.user)
//   // const [triedRefresh, setTriedRefresh] = useState(false)

//   // 1️⃣ Fire refresh exactly once
//   // useEffect(() => {
//   //   console.log("Home useEffect - triedRefresh:", triedRefresh, "accessToken:", accessToken, "roleId:", roleId)
//   //   if (triedRefresh) return
//   //   setTriedRefresh(true);

//   //   (async () => {
//   //     const res = await fetchWithRefresh(
//   //       REFRESH_TOKEN, 
//   //       { method: "POST" ,  credentials: "include",}, 
//   //       router
//   //     )
//   //     // if res is null → fetchWithRefresh already did router.push("/login")
//   //   })()
//   // }, [triedRefresh, router])

//   // 2️⃣ Only after the above has run do we redirect
//   useEffect(() => {
//     console.log("Home second useEffect - triedRefresh:", accessToken, "roleId:", roleId);
    
//     // if (!triedRefresh) return

//     if (!accessToken) {
//       router.replace("/login")
//     } else if (roleId === 1) {
//       router.replace("/usermanagement")
//     } else {
//       router.replace("/dashboard")
//     }
//   }, [accessToken, roleId, router])

//   return null
// }




// app/page.tsx
import { redirect } from "next/navigation";

export default function Home() {
  // unconditionally send everyone landing on “/” to “/login”
  redirect("/login");
}
