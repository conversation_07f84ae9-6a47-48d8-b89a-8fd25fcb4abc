"use client"

import { ArrowRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface TimelineTreatment {
  id: string
  name: string
  teeth: number[]
  estimatedDuration: number
  costRangeLow: number
  costRangeHigh: number
  priority: "immediate" | "short-term" | "preventative"
  rationale: string
  approved: boolean | null
  aiConfidence: number
}

interface TreatmentTimelineProps {
  treatments: TimelineTreatment[]
  onSelectTreatment?: (treatment: TimelineTreatment) => void
  selectedTreatmentId?: string
}

export default function TreatmentTimeline({
  treatments,
  onSelectTreatment,
  selectedTreatmentId,
}: TreatmentTimelineProps) {
  // Order treatments by priority
  const orderedTreatments = [...treatments].sort((a, b) => {
    const priorityOrder = { immediate: 0, "short-term": 1, preventative: 2 }
    return priorityOrder[a.priority] - priorityOrder[b.priority]
  })

  return (
    <div className="w-full">
      <div className="flex items-center overflow-x-auto pb-2">
        {orderedTreatments.map((treatment, index) => (
          <div key={treatment.id} className="flex items-center flex-shrink-0">
            <div
              className={cn(
                "relative rounded-lg p-3 shadow-sm min-w-24 max-w-52 cursor-pointer transition-all",
                treatment.priority === "immediate"
                  ? "bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800"
                  : treatment.priority === "short-term"
                    ? "bg-amber-50 border border-amber-200 dark:bg-amber-900/20 dark:border-amber-800"
                    : "bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800",
                selectedTreatmentId === treatment.id && "ring-2 ring-offset-2",
                treatment.priority === "immediate" && selectedTreatmentId === treatment.id && "ring-red-500",
                treatment.priority === "short-term" && selectedTreatmentId === treatment.id && "ring-amber-500",
                treatment.priority === "preventative" && selectedTreatmentId === treatment.id && "ring-blue-500",
              )}
              onClick={() => onSelectTreatment && onSelectTreatment(treatment)}
            >
              <div className="text-xs font-medium truncate">{treatment.name}</div>
              <div className="text-xs text-slate-500 dark:text-slate-400">Teeth: {treatment.teeth.join(", ")}</div>
            </div>

            {index < orderedTreatments.length - 1 && (
              <div className="mx-2">
                <ArrowRight size={16} className="text-slate-400" />
              </div>
            )}
          </div>
        ))}

        {orderedTreatments.length === 0 && (
          <div className="text-center w-full py-4 text-sm text-slate-500 dark:text-slate-400">
            No approved treatments yet
          </div>
        )}
      </div>

      <div className="mt-6">
        <div className="text-sm font-medium mb-2">Treatment Sequence Summary:</div>
        {orderedTreatments.length > 0 ? (
          <ol className="list-decimal pl-5 text-sm space-y-1">
            {orderedTreatments.map((treatment) => (
              <li
                key={treatment.id}
                className={cn(
                  "cursor-pointer hover:text-blue-600 dark:hover:text-blue-400",
                  selectedTreatmentId === treatment.id && "font-medium text-blue-600 dark:text-blue-400",
                )}
                onClick={() => onSelectTreatment && onSelectTreatment(treatment)}
              >
                {treatment.name} (Teeth: {treatment.teeth.join(", ")})
              </li>
            ))}
          </ol>
        ) : (
          <p className="text-sm text-slate-500 dark:text-slate-400 pl-5">
            No treatments have been approved yet. Approve treatments to see them in the sequence.
          </p>
        )}
        <p className="text-sm text-slate-500 mt-3 dark:text-slate-400">
          <span className="font-medium">Note:</span> This sequence is AI-recommended based on clinical best practices.
          You can reorder treatments by dragging and dropping them in the treatment tabs above.
        </p>
      </div>
    </div>
  )
}
