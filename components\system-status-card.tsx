"use client"

import { useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Activity, CheckCircle, XCircle } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { updateSystemStatus } from "@/store/dentalSlice"

export default function SystemStatusCard() {
  const dispatch = useAppDispatch()
  const { systemStatus } = useAppSelector((state) => state.dental)

  // Determine overall system status
  const isSystemOnline = systemStatus.aiEngine.status === "online" && systemStatus.backend.status === "healthy"
  const overallStatus = isSystemOnline ? "online" : "offline"

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate occasional status changes
      const shouldChangeStatus = Math.random() < 0.05 // 5% chance
      let aiStatus = systemStatus.aiEngine.status
      let backendStatus = systemStatus.backend.status

      if (shouldChangeStatus) {
        const aiStatuses = ["online", "degraded", "offline"] as const
        aiStatus = aiStatuses[Math.floor(Math.random() * aiStatuses.length)]

        const backendStatuses = ["healthy", "maintenance", "unhealthy"] as const
        backendStatus = backendStatuses[Math.floor(Math.random() * backendStatuses.length)]
      }

      dispatch(
        updateSystemStatus({
          aiEngine: {
            ...systemStatus.aiEngine,
            status: aiStatus,
            lastUpdated: new Date().toISOString(),
          },
          backend: {
            ...systemStatus.backend,
            status: backendStatus,
            lastHealthCheck: new Date().toISOString(),
          },
        }),
      )
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [dispatch, systemStatus])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online":
        return <CheckCircle className="text-green-500" size={24} />
      case "offline":
        return <XCircle className="text-red-500" size={24} />
      default:
        return <Activity className="text-gray-500" size={24} />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "text-sm font-medium"

    switch (status) {
      case "online":
        return <Badge className={`${baseClasses} bg-green-100 text-green-800 hover:bg-green-100`}>Online</Badge>
      case "offline":
        return <Badge className={`${baseClasses} bg-red-100 text-red-800 hover:bg-red-100`}>Offline</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  return (
    <Card className="col-span-1">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Activity className="text-blue-600" size={20} />
          System Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getStatusIcon(overallStatus)}
            <span className="text-lg font-medium">System</span>
          </div>
          {getStatusBadge(overallStatus)}
        </div>
      </CardContent>
    </Card>
  )
}
