"use client"

import { useState, useR<PERSON>, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  ZoomIn,
  ZoomOut,
  Square,
  Circle,
  ChevronDown,
  Download,
  Maximize,
  Layers,
  Eye,
  EyeOff,
  Sparkles,
  Grid2X2,
} from "lucide-react"
import type { XrayPosition, Annotation } from "../types"

interface XrayViewerProps {
  selectedImage: number | null
  setSelectedImage: (index: number | null) => void
  uploadedImages: string[]
  xrayPositions: XrayPosition[]
  mockAIAnnotations: { [key: number]: Annotation[] }
}

export default function XrayViewer({
  selectedImage,
  setSelectedImage,
  uploadedImages,
  xrayPositions,
  mockAIAnnotations,
}: XrayViewerProps) {
  const [zoomLevel, setZoomLevel] = useState(100)
  const [annotations, setAnnotations] = useState<{ [key: number]: Annotation[] }>({})
  const [showAnnotations, setShowAnnotations] = useState(true)
  const [showAIAnnotations, setShowAIAnnotations] = useState(true)
  const [brightness, setBrightness] = useState(100)
  const [contrast, setContrast] = useState(100)
  const [invert, setInvert] = useState(false)
  const [showGrid, setShowGrid] = useState(false)
  const [annotationColor, setAnnotationColor] = useState("#FF0000")
  const [annotationLabel, setAnnotationLabel] = useState("")
  const [dentistFeedback, setDentistFeedback] = useState("")

  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleZoomIn = () => {
    setZoomLevel((prevZoom) => Math.min(prevZoom + 10, 300))
  }

  const handleZoomOut = () => {
    setZoomLevel((prevZoom) => Math.max(prevZoom - 10, 50))
  }

  // Canvas drawing effect
  useEffect(() => {
    if (selectedImage === null || !canvasRef.current || !imageRef.current || !containerRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const container = containerRef.current
    const img = imageRef.current

    canvas.width = container.clientWidth
    canvas.height = container.clientHeight

    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.filter = `brightness(${brightness}%) contrast(${contrast}%)${invert ? " invert(100%)" : ""}`

    const scale = zoomLevel / 100
    const imgWidth = img.naturalWidth * scale
    const imgHeight = img.naturalHeight * scale

    const x = (canvas.width - imgWidth) / 2
    const y = (canvas.height - imgHeight) / 2

    ctx.drawImage(img, x, y, imgWidth, imgHeight)

    if (showGrid) {
      ctx.strokeStyle = "rgba(255, 255, 255, 0.3)"
      ctx.lineWidth = 1

      for (let i = 0; i <= canvas.width; i += 50) {
        ctx.beginPath()
        ctx.moveTo(i, 0)
        ctx.lineTo(i, canvas.height)
        ctx.stroke()
      }

      for (let i = 0; i <= canvas.height; i += 50) {
        ctx.beginPath()
        ctx.moveTo(0, i)
        ctx.lineTo(canvas.width, i)
        ctx.stroke()
      }
    }

    if (showAnnotations && showAIAnnotations && mockAIAnnotations[selectedImage]) {
      mockAIAnnotations[selectedImage].forEach((annotation) => {
        ctx.strokeStyle = annotation.color
        ctx.fillStyle = `${annotation.color}33`
        ctx.lineWidth = 2
        ctx.setLineDash([5, 3])

        if (annotation.type === "rectangle") {
          const [start] = annotation.points
          const width = annotation.width || 0
          const height = annotation.height || 0

          ctx.beginPath()
          ctx.rect(start.x, start.y, width, height)
          ctx.fill()
          ctx.stroke()

          if (annotation.label) {
            ctx.fillStyle = annotation.color
            ctx.font = "bold 14px Lato"
            ctx.fillText(annotation.label, start.x, start.y - 5)
          }
        } else if (annotation.type === "circle") {
          const [center] = annotation.points
          const radius = annotation.radius || 0

          ctx.beginPath()
          ctx.arc(center.x, center.y, radius, 0, Math.PI * 2)
          ctx.fill()
          ctx.stroke()

          if (annotation.label) {
            ctx.fillStyle = annotation.color
            ctx.font = "bold 14px Lato"
            ctx.fillText(annotation.label, center.x - radius, center.y - radius - 5)
          }
        }

        ctx.setLineDash([])
      })
    }
  }, [
    selectedImage,
    zoomLevel,
    brightness,
    contrast,
    invert,
    showGrid,
    showAnnotations,
    showAIAnnotations,
    mockAIAnnotations,
  ])

  return (
    <Dialog open={selectedImage !== null} onOpenChange={(open) => !open && setSelectedImage(null)}>
      <DialogContent className="max-w-5xl w-[95vw] max-h-[95vh] flex flex-col p-0 gap-0 overflow-hidden bg-jedai-white border-jedai-gray">
        <DialogHeader className="px-4 py-2 border-b border-jedai-gray bg-jedai-gray/10">
          <div className="flex justify-between items-center">
            <DialogTitle className="text-base text-jedai-navy">
              {selectedImage !== null && xrayPositions[selectedImage]?.label}
            </DialogTitle>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 border-jedai-navy text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                      onClick={handleZoomOut}
                      disabled={zoomLevel <= 50}
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom Out</TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <span className="text-sm w-12 text-center text-jedai-navy font-medium">{zoomLevel}%</span>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 border-jedai-navy text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                      onClick={handleZoomIn}
                      disabled={zoomLevel >= 300}
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom In</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {/* Left sidebar - Annotation tools */}
          <div className="w-12 bg-jedai-gray/20 border-r border-jedai-gray flex flex-col items-center py-2 gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                  >
                    <Maximize className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Select</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Separator className="my-1 w-8 bg-jedai-gray" />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                  >
                    <Square className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Rectangle</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                  >
                    <Circle className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Circle</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div className="flex-1"></div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                    onClick={() => setShowAnnotations(!showAnnotations)}
                  >
                    {showAnnotations ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">{showAnnotations ? "Hide" : "Show"} Annotations</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                    onClick={() => setShowAIAnnotations(!showAIAnnotations)}
                  >
                    {showAIAnnotations ? <Sparkles className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">{showAIAnnotations ? "Hide" : "Show"} AI Annotations</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                    onClick={() => setShowGrid(!showGrid)}
                  >
                    <Grid2X2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">{showGrid ? "Hide" : "Show"} Grid</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Main content - Image viewer */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 relative bg-black overflow-hidden" ref={containerRef}>
              {selectedImage !== null && uploadedImages[selectedImage] && (
                <>
                  <img
                    ref={imageRef}
                    src={uploadedImages[selectedImage] || "/placeholder.svg"}
                    alt={xrayPositions[selectedImage]?.label}
                    className="hidden"
                    onLoad={() => {
                      if (canvasRef.current) {
                        const canvas = canvasRef.current
                        const ctx = canvas.getContext("2d")
                        if (ctx) {
                          ctx.clearRect(0, 0, canvas.width, canvas.height)
                        }
                      }
                    }}
                  />
                  <canvas ref={canvasRef} className="w-full h-full cursor-crosshair" />
                </>
              )}
            </div>

            {/* Bottom toolbar */}
            <div className="h-12 border-t border-jedai-gray bg-jedai-gray/10 flex items-center justify-between px-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="brightness" className="text-xs w-16 text-jedai-navy">
                    Brightness
                  </Label>
                  <Slider
                    id="brightness"
                    min={0}
                    max={200}
                    step={5}
                    value={[brightness]}
                    onValueChange={(value) => setBrightness(value[0])}
                    className="w-24"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="contrast" className="text-xs w-16 text-jedai-navy">
                    Contrast
                  </Label>
                  <Slider
                    id="contrast"
                    min={0}
                    max={200}
                    step={5}
                    value={[contrast]}
                    onValueChange={(value) => setContrast(value[0])}
                    className="w-24"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="invert" className="text-xs text-jedai-navy">
                    Invert
                  </Label>
                  <Switch id="invert" checked={invert} onCheckedChange={setInvert} />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-1 border-jedai-navy text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                    >
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: annotationColor }} />
                      Color
                      <ChevronDown className="h-3 w-3 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-2 bg-jedai-white border-jedai-gray">
                    <div className="grid grid-cols-5 gap-1">
                      {[
                        "#FF0000",
                        "#00FF00",
                        "#0000FF",
                        "#FFFF00",
                        "#FF00FF",
                        "#00FFFF",
                        "#FF8000",
                        "#8000FF",
                        "#0080FF",
                        "#FF0080",
                      ].map((color) => (
                        <Button
                          key={color}
                          variant="ghost"
                          className="w-6 h-6 p-0 rounded-full"
                          style={{ backgroundColor: color }}
                          onClick={() => setAnnotationColor(color)}
                        />
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>

                <Input
                  placeholder="Label"
                  value={annotationLabel}
                  onChange={(e) => setAnnotationLabel(e.target.value)}
                  className="h-8 w-32 text-xs border-jedai-gray"
                />

                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 border-jedai-navy text-jedai-navy hover:bg-jedai-navy hover:text-jedai-white"
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </div>
            </div>
          </div>

          {/* Right sidebar - Annotations list */}
          <div className="w-64 bg-jedai-gray/10 border-l border-jedai-gray flex flex-col">
            <div className="p-3 border-b border-jedai-gray">
              <h3 className="text-sm font-medium  text-jedai-navy">Annotations</h3>
            </div>

            <div className="flex-1 overflow-auto p-2">
              {selectedImage !== null && (
                <div className="space-y-2">
                  {mockAIAnnotations[selectedImage] && mockAIAnnotations[selectedImage].length > 0 && (
                    <div className="flex items-center justify-between mb-3 px-2">
                      <Label htmlFor="show-ai-annotations" className="text-xs font-medium text-jedai-navy">
                        AI Annotations
                      </Label>
                      <Switch
                        id="show-ai-annotations"
                        checked={showAIAnnotations}
                        onCheckedChange={setShowAIAnnotations}
                        className="data-[state=checked]:bg-jedai-gold"
                      />
                    </div>
                  )}

                  {showAIAnnotations &&
                    mockAIAnnotations[selectedImage] &&
                    mockAIAnnotations[selectedImage].length > 0 && (
                      <div className="space-y-2">
                        <h4 className="text-xs font-medium mb-2 px-2 text-jedai-navy ">AI Detected Issues</h4>
                        {mockAIAnnotations[selectedImage].map((annotation) => (
                          <div
                            key={annotation.id}
                            className="p-2 rounded border text-xs bg-jedai-white border-jedai-gray"
                          >
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center gap-1">
                                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: annotation.color }} />
                                <span className="font-medium capitalize text-jedai-navy">
                                  {annotation.toothNumber ? `Tooth #${annotation.toothNumber}` : annotation.type}
                                </span>
                                <Badge
                                  variant="outline"
                                  className={`ml-1 text-[10px] ${
                                    annotation.severity === "severe"
                                      ? "border-red-300 text-red-700"
                                      : annotation.severity === "moderate"
                                        ? "border-amber-300 text-amber-700"
                                        : "border-blue-300 text-blue-700"
                                  }`}
                                >
                                  {annotation.severity}
                                </Badge>
                              </div>
                              <Badge variant="secondary" className="text-[10px] bg-jedai-gold text-jedai-navy">
                                AI
                              </Badge>
                            </div>
                            {annotation.label && <p className="text-slate-600">{annotation.label}</p>}
                          </div>
                        ))}
                      </div>
                    )}

                  {(!annotations[selectedImage] || annotations[selectedImage].length === 0) &&
                    (!showAIAnnotations ||
                      !mockAIAnnotations[selectedImage] ||
                      mockAIAnnotations[selectedImage].length === 0) && (
                      <div className="flex flex-col items-center justify-center h-full text-slate-400 text-sm">
                        <Layers className="h-8 w-8 mb-2 opacity-20" />
                        <p>No annotations yet</p>
                        <p className="text-xs">Use the tools on the left to add annotations</p>
                      </div>
                    )}
                </div>
              )}
            </div>

            {/* Dentist Feedback */}
            <div className="p-3 border-t border-jedai-gray">
              <h3 className="text-sm font-medium text-jedai-navy  mb-2">Dentist Feedback</h3>
              <Textarea
                placeholder="Enter feedback about AI detection here..."
                value={dentistFeedback}
                onChange={(e) => setDentistFeedback(e.target.value)}
                className="border-jedai-gray text-jedai-navy"
                rows={3}
              />
            </div>

            {/* AI Suggested Treatments */}
            {selectedImage !== null && (
              <div className="p-3 border-t border-jedai-gray">
                <h3 className="text-sm font-medium text-jedai-navy mb-2">AI Suggested Treatments</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-jedai-navy">
                      Root Canal Therapy (<span className="font-mono">D3310</span>)
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs border-jedai-gold text-jedai-navy hover:bg-jedai-gold hover:text-jedai-navy"
                    >
                      Add to Plan
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
