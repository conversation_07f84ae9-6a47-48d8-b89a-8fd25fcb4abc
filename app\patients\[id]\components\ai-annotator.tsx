import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";


import {
  Pen,
  Square,
  Circle,
  Eraser,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Move,
  Save,
  X,
  AlertTriangle,
  Plus,
  Eye,
  EyeOff,
  Send,
  Undo,
  Redo,
  ArrowLeft,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type {
  Annotation,
  XrayImage,
  AIFinding,
  SuggestedTreatment,
  AIFeedback,
} from "../types";
import { UPDATE_MANUAL_ANNOTATIONS } from "@/constants/apiRoutes";
import { useRouter } from "next/router";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store/store";
import { setAnnotations, setSlots, setPanoramicImage } from "@/store/fullMouthSeriesSlice";
import { useToast } from "@/hooks/use-toast";
import { FMS_LAYOUT } from "../types";

interface AIAnnotatorProps {
  runAnalysis: any;
  isOpen: boolean;
  onClose: () => void;
  onCloseCallback?: () => void; // Add this
  image: XrayImage | string;
  slotInfo: any;
  highlightedFinding?: string;
  annotations?: Annotation;
  router: any;
  isFullScreen?: boolean;
  allImages?: {
    slots: Record<string, XrayImage | null>;
    panoramicImage: XrayImage | null;
  };
}

type DrawingTool = "pen" | "rectangle" | "circle" | "eraser" | "move" | null;
type AnnotationType = "decay";

interface Point {
  x: number;
  y: number;
}

// Per-image state management interface
interface PerImageState {
  annotations: ManualAnnotation[];
  history: ManualAnnotation[][];
  historyIndex: number;
  storedAnnotations: Annotation;
  isEditingAnnotation: boolean;
  selectedAnnotationId: string | null;
  draggedPointIndex: number | null;
}

interface ManualAnnotation {
  id: string;
  type: "freehand" | "rectangle" | "circle";
  coordinates?: {
    x: number;
    y: number;
    width?: number;
    height?: number;
    radius?: number;
  };
  points?: Point[];
  color: string;
  label?: string;
  category_id?: number;
  segmentation?: number[];
  annotation_source?: string; // Enhanced: Add annotation source for consistent rendering
}

// Enhanced: Per-image annotation storage with complete isolation
interface PerImageAnnotationData {
  manual: ManualAnnotation[];
  stored: Annotation;
  history: ManualAnnotation[][];
  historyIndex: number;
  lastModified: number;
}

const annotationStorage = {
  get: (imageId: string): PerImageAnnotationData | undefined => {
    try {
      const stored = localStorage.getItem(`manual_annotations_${imageId}`);
      return stored ? JSON.parse(stored) : undefined;
    } catch (error) {
      console.error('Error loading manual annotations for imageId:', imageId, error);
      // Clear corrupted data
      try {
        localStorage.removeItem(`manual_annotations_${imageId}`);
      } catch (removeError) {
        console.error('Error removing corrupted annotation data:', removeError);
      }
      return undefined;
    }
  },
  set: (imageId: string, value: PerImageAnnotationData) => {
    try {
      // Memory optimization: Limit annotation data size
      const dataToSave = {
        ...value,
        lastModified: Date.now()
      };

      const serialized = JSON.stringify(dataToSave);
      const maxSize = 1024 * 1024; // 1MB limit per annotation set

      if (serialized.length > maxSize) {
        console.warn(`Annotation data for ${imageId} exceeds size limit (${serialized.length} > ${maxSize}). Truncating.`);
        // Keep only the most recent annotations if data is too large
        const truncatedData = {
          ...dataToSave,
          manual: dataToSave.manual.slice(-50), // Keep last 50 annotations
          history: [dataToSave.manual.slice(-50)]
        };
        localStorage.setItem(`manual_annotations_${imageId}`, JSON.stringify(truncatedData));
      } else {
        localStorage.setItem(`manual_annotations_${imageId}`, serialized);
      }

      console.log(`Saved annotations for imageId: ${imageId}`, dataToSave);
    } catch (error) {
      console.error('Error saving manual annotations for imageId:', imageId, error);
      // If quota exceeded, try to clear old data
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        annotationStorage.clearOldData();
        // Retry with truncated data
        try {
          const truncatedData = {
            ...value,
            manual: value.manual.slice(-20), // Keep only last 20 annotations
            history: [value.manual.slice(-20)],
            lastModified: Date.now()
          };
          localStorage.setItem(`manual_annotations_${imageId}`, JSON.stringify(truncatedData));
        } catch (retryError) {
          console.error('Failed to save even truncated data:', retryError);
        }
      }
    }
  },
  delete: (imageId: string) => {
    try {
      localStorage.removeItem(`manual_annotations_${imageId}`);
      console.log(`Deleted annotations for imageId: ${imageId}`);
    } catch (error) {
      console.error('Error deleting manual annotations for imageId:', imageId, error);
    }
  },
  // Clear all annotation data (for cleanup on close without submit)
  clearAll: () => {
    try {
      const keys = Object.keys(localStorage);
      const annotationKeys = keys.filter(key => key.startsWith('manual_annotations_'));
      annotationKeys.forEach(key => localStorage.removeItem(key));
      console.log(`Cleared ${annotationKeys.length} annotation storage entries`);
    } catch (error) {
      console.error('Error clearing all annotation storage:', error);
    }
  },
  // Get all stored image IDs
  getAllImageIds: (): string[] => {
    try {
      const keys = Object.keys(localStorage);
      return keys
        .filter(key => key.startsWith('manual_annotations_'))
        .map(key => key.replace('manual_annotations_', ''));
    } catch (error) {
      console.error('Error getting all image IDs:', error);
      return [];
    }
  },
  // Clear old annotation data to free up space
  clearOldData: () => {
    try {
      const keys = Object.keys(localStorage);
      const annotationKeys = keys.filter(key => key.startsWith('manual_annotations_'));

      // Sort by last modified time and keep only recent ones
      const keyData = annotationKeys.map(key => {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          return { key, lastModified: data.lastModified || 0 };
        } catch {
          return { key, lastModified: 0 };
        }
      }).sort((a, b) => b.lastModified - a.lastModified);

      // Keep only the 10 most recent annotation sets
      const keysToRemove = keyData.slice(10).map(item => item.key);
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.error('Error removing old annotation key:', key, error);
        }
      });

      console.log(`Cleared ${keysToRemove.length} old annotation entries`);
    } catch (error) {
      console.error('Error clearing old annotation data:', error);
    }
  }
};



const randomColors = {
  TeethNumbering: {
    1: [255, 182, 193, 0.8],
    2: [137, 207, 240, 0.7],
    3: [240, 230, 140, 0.9],
    4: [220, 190, 255, 0.6],
    5: [190, 255, 170, 0.8],
    6: [230, 190, 90, 0.7],
    7: [140, 140, 255, 0.9],
    8: [245, 130, 120, 0.8],
    9: [210, 105, 180, 0.7],
    10: [220, 245, 100, 0.9],
    11: [130, 220, 240, 0.8],
    12: [255, 140, 50, 0.7],
    13: [200, 180, 230, 0.9],
    14: [170, 255, 130, 0.8],
    15: [240, 120, 200, 0.7],
    16: [110, 190, 255, 0.9],
    17: [255, 200, 100, 0.8],
    18: [180, 130, 220, 0.7],
    19: [230, 240, 150, 0.9],
    20: [150, 255, 190, 0.8],
    21: [240, 180, 60, 0.7],
    22: [120, 140, 240, 0.9],
    23: [210, 110, 200, 0.8],
    24: [220, 255, 140, 0.7],
    25: [140, 220, 210, 0.9],
    26: [250, 130, 90, 0.8],
    27: [190, 240, 100, 0.7],
    28: [110, 160, 230, 0.9],
    29: [230, 200, 120, 0.8],
    30: [240, 140, 180, 0.7],
    31: [160, 255, 220, 0.9],
    32: [200, 120, 240, 0.8],
  },
  TeethDecay: {
    1: [255, 245, 157, 0.8], // Light Yellow - Incipient Decay
    2: [255, 215, 0, 0.8], // Dark Yellow - Moderate Decay
    3: [255, 105, 180, 0.8], // Pink - Advanced Decay (Hot Pink)
    4: [255, 0, 0, 0.9], // Red - Severe Decay
  },
};

export function AIAnnotator({
  runAnalysis,
  isOpen,
  onClose,
  onCloseCallback,
  image,
  slotInfo,
  highlightedFinding,
  annotations,
  router,
  isFullScreen = false,
  allImages,
}: AIAnnotatorProps) {
  console.log("AIAnnotator component rendered"); // Force refresh
  const dispatch = useDispatch();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  const canvasRefs = useRef<(HTMLCanvasElement | null)[]>([]);
  const imageRefs = useRef<(HTMLImageElement | null)[]>([]);
  const [currentTool, setCurrentTool] = useState<DrawingTool>(null);
  const [currentAnnotationType] = useState<AnnotationType>("decay");
  const [selectedDecayType, setSelectedDecayType] = useState<string>("1");
  const [isDrawing, setIsDrawing] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastMousePos, setLastMousePos] = useState<Point | null>(null);
  // Enhanced: Per-image state management
  const [perImageStates, setPerImageStates] = useState<Map<string, PerImageState>>(new Map());
  const [currentAnnotations, setCurrentAnnotations] = useState<
    ManualAnnotation[]
  >([]);
  const [history, setHistory] = useState<ManualAnnotation[][]>([[]]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [aiFindings] = useState<AIFinding[]>([]);
  const [showAIFindings, setShowAIFindings] = useState(false);
  const [selectedFinding, setSelectedFinding] = useState<AIFinding | null>(
    null
  );
  const [feedbackText, setFeedbackText] = useState("");
  const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [manualAnnotationFeedback, setManualAnnotationFeedback] = useState("");
  const [justSubmittedFeedback, setJustSubmittedFeedback] = useState(false);
  const [brightness, setBrightness] = useState<number>(100);
  const [contrast, setContrast] = useState<number>(100);
  const [showTeethNumbering, setShowTeethNumbering] = useState(false);
  const [showTeethDecay, setShowTeethDecay] = useState(true);
  const [storedAnnotations, setStoredAnnotations] = useState<
    Annotation | undefined
  >(undefined);
  const [imageDimensions, setImageDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const layoutSwitchProcessedRef = useRef(false);


  // Fixed Button 1 maximum dimensions - consistent sizing with proper aspect ratio
  const BUTTON_1_MAX_DIMENSIONS = {
    width: 800,
    height: 600
  };

  // Helper function to calculate Button 1 canvas dimensions based on image aspect ratio
  const calculateButton1CanvasDimensions = useCallback((imageWidth: number, imageHeight: number) => {
    const maxWidth = BUTTON_1_MAX_DIMENSIONS.width;
    const maxHeight = BUTTON_1_MAX_DIMENSIONS.height;

    const imageAspect = imageWidth / imageHeight;
    const maxAspect = maxWidth / maxHeight;

    let canvasWidth, canvasHeight;

    if (imageAspect > maxAspect) {
      // Image is wider than max aspect ratio
      canvasWidth = maxWidth;
      canvasHeight = maxWidth / imageAspect;
    } else {
      // Image is taller than max aspect ratio
      canvasHeight = maxHeight;
      canvasWidth = maxHeight * imageAspect;
    }

    return {
      width: Math.round(canvasWidth),
      height: Math.round(canvasHeight)
    };
  }, []);

  // Helper function to set Button 1 canvas dimensions consistently
  const setButton1CanvasDimensions = useCallback((canvas: HTMLCanvasElement, imageWidth?: number, imageHeight?: number) => {
    if (imageWidth && imageHeight) {
      const dimensions = calculateButton1CanvasDimensions(imageWidth, imageHeight);
      canvas.width = dimensions.width;
      canvas.height = dimensions.height;
      canvas.style.width = `${dimensions.width}px`;
      canvas.style.height = `${dimensions.height}px`;
    } else {
      // Fallback to max dimensions if image dimensions not available
      canvas.width = BUTTON_1_MAX_DIMENSIONS.width;
      canvas.height = BUTTON_1_MAX_DIMENSIONS.height;
      canvas.style.width = `${BUTTON_1_MAX_DIMENSIONS.width}px`;
      canvas.style.height = `${BUTTON_1_MAX_DIMENSIONS.height}px`;
    }
  }, [calculateButton1CanvasDimensions]);


  const [activeImageCount, setActiveImageCount] = useState<1 | 2 | 4 | 8>(1);
  const [selectedImages, setSelectedImages] = useState<Array<{ slotId: string, image: XrayImage }>>([]);
  const [allAvailableImages, setAllAvailableImages] = useState<Array<{ slotId: string, image: XrayImage }>>([]);
  const [currentMainImage, setCurrentMainImage] = useState<XrayImage | string | null>(null);
  const [isLoadingMultipleImages, setIsLoadingMultipleImages] = useState(false);
  const [imageDrawBounds, setImageDrawBounds] = useState<{
    drawX: number;
    drawY: number;
    drawWidth: number;
    drawHeight: number;
  } | null>(null);
  const [previousActiveImageCount, setPreviousActiveImageCount] = useState<number>(1);
  const longPressTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [imageId, setImageId] = useState<string>("");
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const drawDebounceRef = useRef<NodeJS.Timeout | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const touchStartDistanceRef = useRef<number | null>(null);
  const touchStartZoomRef = useRef<number | null>(null);
  const [isEditingAnnotation, setIsEditingAnnotation] = useState(false);
  const [selectedAnnotationId, setSelectedAnnotationId] = useState<
    string | null
  >(null);
  const [draggedPointIndex, setDraggedPointIndex] = useState<number | null>(
    null
  );
  const userId = useSelector((state: RootState) => state?.user?.userId);
  const globalAnnotations = useSelector((state: RootState) =>
    state.fullMouthSeries.annotations
  );
  const patientName = useSelector((state: RootState) => state.fullMouthSeries.patientName);
  const { toast } = useToast();

  // Memory cleanup function to prevent memory leaks
  const cleanupMemory = useCallback(() => {
    try {
      // Clear unused canvas references
      canvasRefs.current = canvasRefs.current.filter(canvas => canvas !== null);
      imageRefs.current = imageRefs.current.filter(img => img !== null);

      // Force garbage collection of unused images
      imageRefs.current.forEach((img, index) => {
        if (img && !img.complete) {
          imageRefs.current[index] = null;
        }
      });

      // Clear old localStorage data periodically
      if (Math.random() < 0.1) { // 10% chance to run cleanup
        annotationStorage.clearOldData();
      }
    } catch (error) {
      console.error("Error in memory cleanup:", error);
    }
  }, []);

  // Run memory cleanup periodically
  useEffect(() => {
    const cleanupInterval = setInterval(cleanupMemory, 30000); // Every 30 seconds
    return () => clearInterval(cleanupInterval);
  }, [cleanupMemory]);

  console.log("SlotInfo in Ai", slotInfo, image, annotations);



  const FMS_SLOT_LABELS: Record<string, string> = {
    URP2: "Periapical Right Upper 2",
    URP1: "Periapical Right Upper 1",
    UCP1: "Periapical Center Upper 1",
    UCP2: "Periapical Center Upper 2",
    UCP3: "Periapical Center Upper 3",
    ULP1: "Periapical Left Upper 1",
    ULP2: "Periapical Left Upper 2",
    RB1: "Bitewing Right 1",
    RB2: "Bitewing Right 2",
    LB1: "Bitewing Left 1",
    LB2: "Bitewing Left 2",
    LPR2: "Periapical Right Lower 2",
    LPR1: "Periapical Right Lower 1",
    LCP1: "Periapical Center Lower 1",
    LCP2: "Periapical Center Lower 2",
    LCP3: "Periapical Center Lower 3",
    LLP1: "Periapical Left Lower 1",
    LLP2: "Periapical Left Lower 2",
    PANORAMIC: "Panoramic",

    // Additional keys with "MIS " prefix and values with "Miscellaneous " prefix
    MISURP2: "Miscellaneous Periapical Right Upper 2",
    MISURP1: "Miscellaneous Periapical Right Upper 1",
    MISUCP1: "Miscellaneous Periapical Center Upper 1",
    MISUCP2: "Miscellaneous Periapical Center Upper 2",
    MISUCP3: "Miscellaneous Periapical Center Upper 3",
    MISULP1: "Miscellaneous Periapical Left Upper 1",
    MISULP2: "Miscellaneous Periapical Left Upper 2",
    MISULP3: "Miscellaneous Periapical Left Upper 3",
    MISRB1: "Miscellaneous Bitewing Right 1",
    MISRB2: "Miscellaneous Bitewing Right 2",
    MISRB3: "Miscellaneous Bitewing Right 3",
    MISLB1: "Miscellaneous Bitewing Left 1",
    MISLB2: "Miscellaneous Bitewing Left 2",
    MISLB3: "Miscellaneous Bitewing Left 3",
    MISLPR2: "Miscellaneous Periapical Right Lower 2",
    MISLPR1: "Miscellaneous Periapical Right Lower 1",
    MISLCP1: "Miscellaneous Periapical Center Lower 1",
    MISLCP2: "Miscellaneous Periapical Center Lower 2",
    MISLCP3: "Miscellaneous Periapical Center Lower 3",
    MISLLP1: "Miscellaneous Periapical Left Lower 1",
    MISLLP2: "Miscellaneous Periapical Left Lower 2",
    MISLLP3: "Miscellaneous Periapical Left Lower 3",

  };

  const decayDropdownOptions = [
    { label: "Incipient Decay", value: "1" },
    { label: "Moderate Decay", value: "2" },
    { label: "Advanced Decay", value: "3" },
    { label: "Severe Decay", value: "4" },
  ];

  const getAnnotationColor = (decayValue: string) => {
    const value = Number(decayValue);
    const color = (randomColors.TeethDecay as any)[value] || [255, 0, 0, 0.9];
    return `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${color[3]})`;
  };

  const clampCoordinates = (
    x: number,
    y: number,
    canvasWidth: number,
    canvasHeight: number
  ) => {
    return {
      x: Math.max(0, Math.min(x, canvasWidth)),
      y: Math.max(0, Math.min(y, canvasHeight)),
    };
  };

  const getImageUrl = useCallback(() => {
    // Enhanced: Use currentMainImage if available, otherwise fall back to original image
    const imageToUse = currentMainImage || image;
    if (typeof imageToUse === "string") return imageToUse;
    if (imageToUse && typeof imageToUse === "object" && "url" in imageToUse) return imageToUse.url;
    return null;
  }, [currentMainImage, image]);

  const getImageId = useCallback(() => {
    // Enhanced: Use currentMainImage if available, otherwise fall back to original image
    const imageToUse = currentMainImage || image;
    if (typeof imageToUse === "object" && imageToUse && "imageId" in imageToUse)
      return String(imageToUse.imageId);
    return getImageUrl() || "unknown";
  }, [currentMainImage, image, getImageUrl]);

  // Enhanced: Per-image state management helpers

  const updatePerImageState = useCallback((imageId: string, updates: Partial<PerImageState>) => {
    setPerImageStates(prev => {
      const newMap = new Map(prev);
      const currentState = prev.get(imageId) || {
        annotations: [],
        history: [[]],
        historyIndex: 0,
        storedAnnotations: { numbering: [], decay: [] },
        isEditingAnnotation: false,
        selectedAnnotationId: null,
        draggedPointIndex: null
      };
      newMap.set(imageId, { ...currentState, ...updates });
      return newMap;
    });
  }, []);

  const switchToImageState = useCallback((imageId: string) => {
    // Load from localStorage if available
    const savedData = annotationStorage.get(imageId);
    if (savedData && savedData.manual && savedData.manual.length > 0) {
      // Only restore if there are actual annotations (not empty array from eraser)
      setCurrentAnnotations(savedData.manual);
      setHistory(savedData.history || [savedData.manual]);
      setHistoryIndex(savedData.historyIndex || 0);
      setStoredAnnotations(savedData.stored || { numbering: [], decay: [] });
    } else {
      // Use defaults for new image or when annotations were erased
      setCurrentAnnotations([]);
      setHistory([[]]);
      setHistoryIndex(0);
      setStoredAnnotations(savedData?.stored || { numbering: [], decay: [] });
    }

    // Always reset editing state when switching images
    setIsEditingAnnotation(false);
    setSelectedAnnotationId(null);
    setDraggedPointIndex(null);

    console.log(`Switched to image state for: ${imageId}`, { savedData });
  }, []); // No dependencies to prevent re-renders

  // Helper function to check if an image has decay
  const hasDecay = useCallback((slotId: string) => {
    const slotKey = slotId.toUpperCase();
    const imageAnnotations = globalAnnotations?.[slotKey];
    return imageAnnotations?.decay && imageAnnotations.decay.length > 0;
  }, [globalAnnotations]);

  // Function to render annotations on slider thumbnails - Simplified direct approach
  const renderSliderAnnotations = useCallback((
    canvas: HTMLCanvasElement,
    slotId: string,
    imageElement: HTMLImageElement
  ) => {
    const ctx = canvas.getContext('2d');
    if (!ctx || !imageElement.complete) {
      return;
    }

    const slotKey = slotId.toUpperCase();
    const imageAnnotations = globalAnnotations?.[slotKey];
    if (!imageAnnotations) {
      return;
    }

    // Enhanced: Calculate precise image display area within container
    const container = imageElement.parentElement;
    if (!container) return;

    // Get container dimensions (128px x 128px for w-32 h-32)
    const containerWidth = 128;
    const containerHeight = 128;

    // Get natural image dimensions
    const naturalWidth = imageElement.naturalWidth;
    const naturalHeight = imageElement.naturalHeight;

    // Calculate aspect ratios
    const containerAspect = containerWidth / containerHeight;
    const imageAspect = naturalWidth / naturalHeight;

    // Calculate actual displayed image dimensions using object-fit: contain logic
    let displayWidth, displayHeight, offsetX, offsetY;

    if (imageAspect > containerAspect) {
      // Image is wider - fit to container width
      displayWidth = containerWidth;
      displayHeight = containerWidth / imageAspect;
      offsetX = 0;
      offsetY = (containerHeight - displayHeight) / 2;
    } else {
      // Image is taller - fit to container height
      displayHeight = containerHeight;
      displayWidth = containerHeight * imageAspect;
      offsetX = (containerWidth - displayWidth) / 2;
      offsetY = 0;
    }

    // Set canvas to match the actual displayed image area exactly
    canvas.width = displayWidth;
    canvas.height = displayHeight;
    canvas.style.width = `${displayWidth}px`;
    canvas.style.height = `${displayHeight}px`;
    canvas.style.position = 'absolute';
    canvas.style.top = `${offsetY}px`;
    canvas.style.left = `${offsetX}px`;
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '10';

    // Clear canvas
    ctx.clearRect(0, 0, displayWidth, displayHeight);
    ctx.save();

    // Enhanced: Precise coordinate scaling for pinpoint accuracy
    const normalizeCoordinates = (point: { x: number; y: number }) => {
      // Scale from natural image coordinates to display coordinates
      const scaleX = displayWidth / naturalWidth;
      const scaleY = displayHeight / naturalHeight;

      // Apply scaling - coordinates are now perfectly aligned
      const x = point.x * scaleX;
      const y = point.y * scaleY;

      return {
        x: Math.max(0, Math.min(x, displayWidth)),
        y: Math.max(0, Math.min(y, displayHeight)),
      };
    };

    // Simplified polygon drawing function
    const drawPolygon = (
      finding: any,
      colorMap: Record<number, number[]>,
      isNumbering: boolean = false
    ) => {
      if (!finding.segmentation || !Array.isArray(finding.segmentation)) {
        return;
      }

      const segmentations = Array.isArray(finding.segmentation[0])
        ? finding.segmentation
        : [finding.segmentation];

      segmentations.forEach((points: number[]) => {
        if (!Array.isArray(points) || points.length < 4 || points.length % 2 !== 0) {
          return;
        }

        const normalizedPoints: { x: number; y: number }[] = [];
        ctx.beginPath();

        for (let i = 0; i < points.length; i += 2) {
          const x = points[i];
          const y = points[i + 1];

          if (typeof x !== "number" || typeof y !== "number" || isNaN(x) || isNaN(y)) {
            return;
          }

          const point = normalizeCoordinates({ x, y });
          normalizedPoints.push(point);

          if (i === 0) {
            ctx.moveTo(point.x, point.y);
          } else {
            ctx.lineTo(point.x, point.y);
          }
        }
        ctx.closePath();

        const color = colorMap[String(finding.category_id)] || [255, 0, 0];

        if (isNumbering) {
          // Teeth numbering: outline with tooth number - enhanced accuracy
          ctx.fillStyle = "transparent";
          ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.stroke();

          // Enhanced tooth number positioning for better accuracy
          if (normalizedPoints.length > 0) {
            // Calculate geometric center for better positioning
            const centerX = normalizedPoints.reduce((sum, p) => sum + p.x, 0) / normalizedPoints.length;
            const centerY = normalizedPoints.reduce((sum, p) => sum + p.y, 0) / normalizedPoints.length;

            // Enhanced text styling for better visibility on thumbnails
            ctx.fillStyle = "#000000";
            ctx.strokeStyle = "#FFFFFF";
            ctx.lineWidth = 3;
            ctx.font = "bold 12px Arial";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";

            // Draw text with outline for better visibility
            ctx.strokeText(String(finding.category_id), centerX, centerY);
            ctx.fillText(String(finding.category_id), centerX, centerY);
          }
        } else {
          // Teeth decay: filled with outline (no badges in slider)
          const isManual = finding.annotation_source === "Manual";
          const fillOpacity = isManual ? 0.6 : 0.4;
          ctx.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${fillOpacity})`;
          ctx.fill();
          ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
          ctx.lineWidth = isManual ? 2 : 1.5;
          ctx.setLineDash([]);
          ctx.stroke();

          // Removed: AI/M badges for slider thumbnails as requested
        }
      });
    };

    // Enhanced: Render both stored annotations and manual annotations
    if (showTeethDecay && imageAnnotations.decay) {
      imageAnnotations.decay.forEach((finding) =>
        drawPolygon(finding, randomColors.TeethDecay, false)
      );
    }

    // Enhanced: Render manual annotations for this specific image
    const imageId = slotId; // Use slotId as the image identifier for thumbnails
    const manualAnnotationData = annotationStorage.get(imageId);
    if (manualAnnotationData && manualAnnotationData.manual && manualAnnotationData.manual.length > 0) {
      // Draw manual annotations with proper scaling
      manualAnnotationData.manual.forEach((annotation) => {
        if (annotation.type === "freehand" && annotation.points && annotation.points.length > 0) {
          ctx.beginPath();
          ctx.strokeStyle = annotation.color || "#FF0000";
          ctx.fillStyle = `${annotation.color || "#FF0000"}40`; // Semi-transparent fill
          ctx.lineWidth = 2;
          ctx.setLineDash([]);

          // Scale and draw the manual annotation points
          const scaledPoints = annotation.points.map(point => normalizeCoordinates(point));

          if (scaledPoints.length > 0) {
            ctx.moveTo(scaledPoints[0].x, scaledPoints[0].y);
            for (let i = 1; i < scaledPoints.length; i++) {
              ctx.lineTo(scaledPoints[i].x, scaledPoints[i].y);
            }
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
          }
        }
      });
    }

    ctx.restore();
  }, [globalAnnotations, showTeethDecay, randomColors, annotationStorage]);

  // Enhanced: Refresh thumbnails when annotations change
  useEffect(() => {
    // Refresh all thumbnail canvases when global annotations change
    const thumbnailImages = document.querySelectorAll('.annotation-canvas');
    thumbnailImages.forEach(canvas => {
      const container = canvas.parentElement;
      if (container) {
        const img = container.querySelector('img');
        if (img && img.complete) {
          // Re-trigger the annotation rendering
          const slotId = img.alt;
          if (slotId) {
            // Remove existing canvas
            container.removeChild(canvas);
            // Create new canvas
            const newCanvas = document.createElement('canvas');
            newCanvas.className = 'annotation-canvas';
            renderSliderAnnotations(newCanvas, slotId, img);
            container.appendChild(newCanvas);
          }
        }
      }
    });
  }, [globalAnnotations, showTeethDecay, showTeethNumbering, renderSliderAnnotations]);

  // Use the current selected image's slot ID if available, otherwise fall back to slotInfo
  const currentSlotId = selectedImages.length > 0 ? selectedImages[0].slotId : slotInfo?.id;
  const slotKey = currentSlotId
    ? currentSlotId.toUpperCase()
    : getImageId().toUpperCase()


  useEffect(() => {
    // Always prioritize Redux store for annotations to ensure consistency
    const reduxAnn = globalAnnotations?.[slotKey] || {
      numbering: [],
      decay: [],
    };

    setStoredAnnotations(reduxAnn);
    console.log("Loading annotations for slot", slotKey, ":", reduxAnn);
  }, [slotKey, globalAnnotations]); // Removed annotations prop dependency

  const normalizeAndClampCoordinates = (
    point: { x: number; y: number },
    canvasWidth: number,
    canvasHeight: number,
    imageWidth: number,
    imageHeight: number
  ) => {
    // Enhanced coordinate normalization for 100% accuracy across all scenarios
    if (imageDrawBounds) {
      const { drawX, drawY, drawWidth, drawHeight } = imageDrawBounds;

      // Calculate precise scaling factors for pixel-perfect alignment
      const scaleX = drawWidth / imageWidth;
      const scaleY = drawHeight / imageHeight;

      const normalized = {
        x: drawX + (point.x * scaleX),
        y: drawY + (point.y * scaleY),
      };



      // Strict clamping to image boundaries for 100% accuracy
      return {
        x: Math.round(Math.max(drawX, Math.min(normalized.x, drawX + drawWidth - 1))),
        y: Math.round(Math.max(drawY, Math.min(normalized.y, drawY + drawHeight - 1))),
      };
    }

    // Enhanced fallback logic with precise aspect ratio handling
    const scaleX = canvasWidth / imageWidth;
    const scaleY = canvasHeight / imageHeight;
    const scale = Math.min(scaleX, scaleY);

    const scaledImageWidth = imageWidth * scale;
    const scaledImageHeight = imageHeight * scale;

    const offsetX = (canvasWidth - scaledImageWidth) / 2;
    const offsetY = (canvasHeight - scaledImageHeight) / 2;

    const normalized = {
      x: offsetX + (point.x * scale),
      y: offsetY + (point.y * scale),
    };

    return {
      x: Math.round(Math.max(offsetX, Math.min(normalized.x, offsetX + scaledImageWidth - 1))),
      y: Math.round(Math.max(offsetY, Math.min(normalized.y, offsetY + scaledImageHeight - 1))),
    };
  };

  const denormalizeCoordinates = (
    point: { x: number; y: number },
    canvasWidth: number,
    canvasHeight: number,
    imageWidth: number,
    imageHeight: number
  ) => {
    const x = (point.x / canvasWidth) * imageWidth;
    const y = (point.y / canvasHeight) * imageHeight;
    return {
      x: Math.round(Math.max(0, Math.min(x, imageWidth))),
      y: Math.round(Math.max(0, Math.min(y, imageHeight))),
    };
  };

  const drawCanvas = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const ctx = canvas?.getContext("2d");
      const img = imageRef.current;

      if (!canvas || !ctx || !img || !imageDimensions) return;

      // Memory optimization: Check canvas size to prevent ArrayBuffer allocation errors
      const maxCanvasArea = 4096 * 4096; // 16MP limit
      const canvasArea = canvas.width * canvas.height;
      if (canvasArea > maxCanvasArea) {
        console.warn(`Canvas area (${canvasArea}) exceeds safe limit (${maxCanvasArea}). Skipping draw.`);
        return;
      }

      ctx.save();
      try {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      } catch (clearError) {
        console.error("Error clearing canvas:", clearError);
        ctx.restore();
        return;
      }

      ctx.filter = `brightness(${brightness}%) contrast(${contrast}%)`;

      // Calculate image dimensions to fit canvas while maintaining aspect ratio
      const canvasAspect = canvas.width / canvas.height;
      const imageAspect = img.width / img.height;

      let drawWidth, drawHeight, drawX, drawY;

      if (imageAspect > canvasAspect) {
        // Image is wider than canvas
        drawWidth = canvas.width;
        drawHeight = canvas.width / imageAspect;
        drawX = 0;
        drawY = (canvas.height - drawHeight) / 2;
      } else {
        // Image is taller than canvas
        drawHeight = canvas.height;
        drawWidth = canvas.height * imageAspect;
        drawX = (canvas.width - drawWidth) / 2;
        drawY = 0;
      }

      // Store image drawing bounds for accurate annotation positioning
      setImageDrawBounds({ drawX, drawY, drawWidth, drawHeight });

      ctx.translate(
        canvas.width / 2 + panOffset.x,
        canvas.height / 2 + panOffset.y
      );
      ctx.rotate((rotation * Math.PI) / 180);
      ctx.scale(zoom, zoom);
      ctx.translate(-canvas.width / 2, -canvas.height / 2);

      // Draw image with proper aspect ratio and centering
      ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

      const drawPolygon = (
        finding: any,
        colorMap: Record<number, number[]>,
        isNumbering: boolean
      ) => {
        const segmentations = Array.isArray(finding?.segmentation[0])
          ? finding.segmentation
          : [finding.segmentation];

        segmentations.forEach((points: number[]) => {
          if (points?.length < 2) return;

          ctx.beginPath();
          const allPoints: { x: number; y: number }[] = [];
          for (let i = 0; i < points.length; i += 2) {
            const point = normalizeAndClampCoordinates(
              { x: points[i], y: points[i + 1] },
              canvas.width,
              canvas.height,
              imageDimensions.width,
              imageDimensions.height
            );
            allPoints.push(point);
            if (i === 0) ctx.moveTo(point.x, point.y);
            else ctx.lineTo(point.x, point.y);
          }
          ctx.closePath();

          const categoryId = Number(finding.category_id);
          const color = colorMap[String(categoryId)] || [255, 0, 0, 0.5];
          const isManual = finding.annotation_source === "Manual";

          if (isNumbering) {
            // Fixed: Consistent styling for teeth_numbering (matches multi-image styling)
            ctx.fillStyle = "transparent";
            ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
            ctx.lineWidth = isManual ? 3 : 2; // Uniform line thickness
            ctx.setLineDash([]);
            ctx.stroke();

            if (allPoints.length > 0) {
              const centerX =
                allPoints.reduce((acc, pt) => acc + pt.x, 0) / allPoints.length;
              const centerY =
                allPoints.reduce((acc, pt) => acc + pt.y, 0) / allPoints.length;

              // Fixed: Standardized font styling to match multi-image canvases
              ctx.fillStyle = "#000000";
              ctx.font = "bold 16px Arial"; // Changed from 18px to 16px for consistency
              ctx.textAlign = "center";
              ctx.textBaseline = "middle";
              ctx.fillText(`${categoryId}`, centerX, centerY);
            }
          } else {
            // Enhanced: Different styling for manual vs AI annotations in Button 1 layout
            if (isManual) {
              // Manual annotations: Only outline, no fill (consistent with multi-image layouts)
              ctx.fillStyle = "transparent";
              ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
              ctx.lineWidth = 3;
              ctx.setLineDash([]);
              ctx.stroke();
            } else {
              // AI annotations: Keep original fill + outline
              const fillOpacity = 0.3;
              ctx.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${fillOpacity})`;
              ctx.fill();
              ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
              ctx.lineWidth = 2;
              ctx.setLineDash([]);
              ctx.stroke();
            }

            // Add AI/M badges for teeth_decay annotations in Button 1
            if (allPoints.length > 0) {
              const lowestPoint = allPoints.reduce(
                (lowest, point) => (point.y > lowest.y ? point : lowest),
                allPoints[0]
              );

              const badgeText = isManual ? "M" : "AI";
              const badgeWidth = 20;
              const badgeHeight = 16;
              const badgePadding = 5;

              // Position badge below the lowest point of the polygon
              const badgeX = Math.max(
                0,
                Math.min(
                  lowestPoint.x - badgeWidth / 2,
                  canvas.width - badgeWidth
                )
              );
              const badgeY = Math.max(
                0,
                Math.min(
                  lowestPoint.y + badgePadding,
                  canvas.height - badgeHeight
                )
              );

              ctx.fillStyle = isManual ? "#FF6347" : "#00BFFF";
              ctx.fillRect(badgeX, badgeY, badgeWidth, badgeHeight);
              ctx.fillStyle = "white";
              ctx.font = "10px Arial";
              ctx.textAlign = "center";
              ctx.textBaseline = "middle";
              ctx.fillText(
                badgeText,
                badgeX + badgeWidth / 2,
                badgeY + badgeHeight / 2
              );
            }
          }
          ctx.setLineDash([]);
        });
      };

      if (showTeethNumbering && storedAnnotations?.numbering) {
        storedAnnotations.numbering.forEach((finding) => {
          drawPolygon(finding, randomColors.TeethNumbering, true);
        });
      }

      if (showTeethDecay && storedAnnotations?.decay) {
        storedAnnotations.decay.forEach((finding) => {
          drawPolygon(finding, randomColors.TeethDecay, false);
        });
      }

      if (showAIFindings) {
        aiFindings?.forEach((finding) => {
          const isHighlighted = highlightedFinding === finding.id;
          ctx.strokeStyle = isHighlighted ? "#FFD700" : "#00BFFF";
          ctx.fillStyle = isHighlighted ? "#FFD70030" : "#00BFFF20";
          ctx.lineWidth = isHighlighted ? 4 : 3;
          ctx.setLineDash(isHighlighted ? [10, 5] : [5, 5]);

          const { x, y, width, height } = finding.coordinates;
          const clampedX = Math.max(0, Math.min(x, canvas.width - width));
          const clampedY = Math.max(0, Math.min(y, canvas.height - height));
          ctx.strokeRect(clampedX, clampedY, width, height);
          ctx.fillRect(clampedX, clampedY, width, height);

          ctx.fillStyle = isHighlighted ? "#FFD700" : "#00BFFF";
          ctx.fillRect(clampedX, clampedY - 25, 80, 20);
          ctx.fillStyle = "white";
          ctx.font = "12px Arial";
          ctx.fillText(
            `${finding.procedureCode} ${Math.round(finding.confidence * 100)}%`,
            clampedX + 5,
            clampedY - 10
          );

          ctx.setLineDash([]);
        });
      }

      currentAnnotations?.forEach((annotation) => {
        // Enhanced: Check if annotation is manual (consistent with multi-image layout logic)
        const isManual = annotation.annotation_source === "Manual";

        // Enhanced: Apply consistent styling for manual annotations (outline only, no fill)
        ctx.strokeStyle = annotation.color;
        ctx.lineWidth = isManual ? 3 : 2; // Slightly thicker line for manual annotations
        ctx.setLineDash([]);

        if (annotation.type === "rectangle" && annotation.coordinates) {
          let { x, y, width = 50, height = 50 } = annotation.coordinates;
          x = Math.max(0, Math.min(x, canvas.width - width));
          y = Math.max(0, Math.min(y, canvas.height - height));

          // Enhanced: No fill for manual annotations, consistent with multi-image layouts
          if (!isManual) {
            ctx.fillStyle = annotation.color;
            ctx.fillRect(x, y, width, height);
          }
          ctx.strokeRect(x, y, width, height);
        } else if (annotation.type === "circle" && annotation.coordinates) {
          let { x, y, radius = 25 } = annotation.coordinates;
          x = Math.max(radius, Math.min(x, canvas.width - radius));
          y = Math.max(radius, Math.min(y, canvas.height - radius));
          ctx.beginPath();
          ctx.arc(x, y, radius, 0, 2 * Math.PI);

          // Enhanced: No fill for manual annotations, consistent with multi-image layouts
          if (!isManual) {
            ctx.fillStyle = annotation.color;
            ctx.fill();
          }
          ctx.stroke();
        } else if (annotation.type === "freehand" && annotation.points) {
          ctx.beginPath();
          annotation?.points?.forEach((point, index) => {
            const clamped = clampCoordinates(
              point.x,
              point.y,
              canvas.width,
              canvas.height
            );
            if (index === 0) ctx.moveTo(clamped.x, clamped.y);
            else ctx.lineTo(clamped.x, clamped.y);
          });
          if (annotation.points?.length > 1) {
            const startPoint = annotation.points[0];
            const clampedStart = clampCoordinates(
              startPoint.x,
              startPoint.y,
              canvas.width,
              canvas.height
            );
            ctx.lineTo(clampedStart.x, clampedStart.y);
          }

          // Enhanced: No fill for manual annotations, consistent with multi-image layouts
          if (!isManual) {
            ctx.fillStyle = annotation.color;
            ctx.fill();
          }
          ctx.stroke();

          // Enhanced: Add "M" badge only for manual annotations (consistent with multi-image layouts)
          if (isManual && annotation.points && annotation.points.length > 0) {
            // Find the lowest point for badge placement
            const lowestPoint = annotation.points.reduce(
              (lowest, point) => (point.y > lowest.y ? point : lowest),
              annotation.points[0]
            );

            const badgeText = "M";
            const badgeWidth = 20;
            const badgeHeight = 16;
            const badgePadding = 5;

            // Position badge below the lowest point of the polygon
            const badgeX = Math.max(
              0,
              Math.min(
                lowestPoint.x - badgeWidth / 2,
                canvas.width - badgeWidth
              )
            );
            const badgeY = Math.max(
              0,
              Math.min(
                lowestPoint.y + badgePadding,
                canvas.height - badgeHeight
              )
            );

            // Draw badge background (red for manual)
            ctx.fillStyle = "#FF6347";
            ctx.fillRect(badgeX, badgeY, badgeWidth, badgeHeight);

            // Draw badge text
            ctx.fillStyle = "white";
            ctx.font = "bold 10px Arial";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(
              badgeText,
              badgeX + badgeWidth / 2,
              badgeY + badgeHeight / 2
            );
          }

          if (
            isEditingAnnotation &&
            annotation.id === selectedAnnotationId &&
            annotation.points
          ) {
            ctx.fillStyle = "#FFFF00";
            ctx.strokeStyle = "#000000";
            ctx.lineWidth = 1;
            const sampledPoints = annotation.points.filter(
              (_, index) => index % 5 === 0
            );
            sampledPoints?.forEach((point) => {
              const clamped = clampCoordinates(
                point.x,
                point.y,
                canvas.width,
                canvas.height
              );
              ctx.beginPath();
              ctx.arc(clamped.x, clamped.y, 5 / zoom, 0, 2 * Math.PI);
              ctx.fill();
              ctx.stroke();
            });
          }
        }



      });

      ctx.restore();
    } catch (error) {
      console.error("Error in drawCanvas:", error);
      // Attempt to restore context if possible
      try {
        const canvas = canvasRef.current;
        const ctx = canvas?.getContext("2d");
        if (ctx) {
          ctx.restore();
        }
      } catch (restoreError) {
        console.error("Error restoring canvas context:", restoreError);
      }
    }
  }, [
    zoom,
    rotation,
    panOffset,
    showAIFindings,
    highlightedFinding,
    brightness,
    contrast,
    showTeethNumbering,
    showTeethDecay,
    storedAnnotations,
    currentAnnotations,
    aiFindings,
    imageDimensions,
    isEditingAnnotation,
    selectedAnnotationId,
  ]);

  // Enhanced function to draw multiple images with stable annotation alignment
  const drawMultipleImages = useCallback(() => {
    if (activeImageCount === 1 || isLoadingMultipleImages) return;

    setIsLoadingMultipleImages(true);
    let loadedCount = 0;
    const totalImages = selectedImages.length;

    // Fixed: Clear any existing animation frames to prevent conflicts
    if (drawDebounceRef.current) {
      clearTimeout(drawDebounceRef.current);
    }

    selectedImages.forEach((selectedImg, index) => {
      const canvas = canvasRefs.current[index];
      const ctx = canvas?.getContext("2d");

      if (!canvas || !ctx) return;

      // Create and load image for this canvas with memory optimization
      const img = new Image();
      img.crossOrigin = "anonymous";

      img.onload = () => {
        try {
          // Enhanced: Dynamic canvas sizing based on available space - pixel perfect
          const dynamicSizes = getDynamicImageSize();
          const canvasWidth = parseInt(dynamicSizes.width);
          const canvasHeight = parseInt(dynamicSizes.height);

          // Memory optimization: Limit canvas size to prevent ArrayBuffer allocation errors
          const maxCanvasSize = 2048; // Maximum dimension to prevent memory issues
          const limitedWidth = Math.min(canvasWidth, maxCanvasSize);
          const limitedHeight = Math.min(canvasHeight, maxCanvasSize);

          // Set canvas dimensions to match image grid box exactly for pixel-perfect annotations
          canvas.width = limitedWidth;
          canvas.height = limitedHeight;

          // Enhanced: Force canvas style dimensions to match image grid box exactly
          canvas.style.width = dynamicSizes.width;
          canvas.style.height = dynamicSizes.height;

          // Clear canvas after sizing with error handling
          try {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
          } catch (clearError) {
            console.error(`Error clearing canvas ${index}:`, clearError);
            return; // Skip this canvas if clearing fails
          }

          // Calculate image dimensions to fit canvas while maintaining aspect ratio
          const canvasAspect = canvas.width / canvas.height;
          const imageAspect = img.width / img.height;

          let drawWidth, drawHeight, drawX, drawY;

          if (imageAspect > canvasAspect) {
            // Image is wider than canvas
            drawWidth = canvas.width;
            drawHeight = canvas.width / imageAspect;
            drawX = 0;
            drawY = (canvas.height - drawHeight) / 2;
          } else {
            // Image is taller than canvas
            drawHeight = canvas.height;
            drawWidth = canvas.height * imageAspect;
            drawX = (canvas.width - drawWidth) / 2;
            drawY = 0;
          }

          // Apply filters and transformations
          ctx.save();
          ctx.filter = `brightness(${brightness}%) contrast(${contrast}%)`;

          // Draw the image
          ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

          // Enhanced: Draw both teeth_decay and teeth_numbering annotations with 100% accuracy
          drawAnnotationsForImage(ctx, canvas, selectedImg.slotId, drawX, drawY, drawWidth, drawHeight, img.width, img.height);

          ctx.restore();

          // Track loading completion
          loadedCount++;
          if (loadedCount === totalImages) {
            setIsLoadingMultipleImages(false);
          }
        } catch (error) {
          console.error(`Error processing image ${index}:`, error);
          // Track loading completion even on error
          loadedCount++;
          if (loadedCount === totalImages) {
            setIsLoadingMultipleImages(false);
          }
        }
      };

      img.onerror = () => {
        console.error(`Failed to load image: ${selectedImg.image.url}`);
        loadedCount++;
        if (loadedCount === totalImages) {
          setIsLoadingMultipleImages(false);
        }
      };

      img.src = selectedImg.image.url;
    });
  }, [selectedImages, activeImageCount, brightness, contrast, showTeethNumbering, showTeethDecay, isLoadingMultipleImages]);

  // Enhanced debounced version of drawMultipleImages with memory optimization and error handling
  const debouncedDrawMultipleImages = useCallback(() => {
    if (drawDebounceRef.current) {
      clearTimeout(drawDebounceRef.current);
    }

    drawDebounceRef.current = setTimeout(() => {
      try {
        // Only draw if we're still in multi-image mode
        if (activeImageCount > 1 && selectedImages.length >= 1) {
          drawMultipleImages();
        }
      } catch (error) {
        console.error("Error in debouncedDrawMultipleImages:", error);
        // Graceful fallback - clear canvases to prevent corruption
        canvasRefs.current.forEach((canvas, index) => {
          if (canvas) {
            try {
              const ctx = canvas.getContext('2d');
              if (ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
              }
            } catch (clearError) {
              console.error(`Error clearing canvas ${index}:`, clearError);
            }
          }
        });
      }
    }, 50); // Reduced debounce time for faster rendering
  }, [drawMultipleImages, activeImageCount, selectedImages.length]);

  // Enhanced: Immediate multi-image rendering with anti-flickering measures
  useEffect(() => {
    if (activeImageCount > 1 && selectedImages.length > 0) {
      // Enhanced: Immediate rendering for layout switches to prevent shrinking
      const animationId = requestAnimationFrame(() => {
        // Force immediate rendering for Button 2, 4, 8 scenarios
        drawMultipleImages();

        // Follow up with debounced rendering for stability
        setTimeout(() => {
          debouncedDrawMultipleImages();
        }, 50); // Reduced delay for faster response
      });

      return () => cancelAnimationFrame(animationId);
    }
  }, [activeImageCount, drawMultipleImages, debouncedDrawMultipleImages, selectedImages.length, showTeethNumbering, showTeethDecay]);

  // Removed window resize listener to prevent complexity and focus on fixed sizing

  // Function to draw annotations for a specific image
  const drawAnnotationsForImage = useCallback((
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    slotId: string,
    drawX: number,
    drawY: number,
    drawWidth: number,
    drawHeight: number,
    originalWidth: number,
    originalHeight: number
  ) => {
    // Get annotations for this specific slot from global annotations
    const slotKey = slotId.toUpperCase();
    const imageAnnotations = globalAnnotations?.[slotKey] || { numbering: [], decay: [] };

    // Helper function to convert coordinates from original image to canvas
    const convertCoordinates = (x: number, y: number) => {
      const scaleX = drawWidth / originalWidth;
      const scaleY = drawHeight / originalHeight;
      return {
        x: drawX + (x * scaleX),
        y: drawY + (y * scaleY)
      };
    };

    const drawPolygonForImage = (
      finding: any,
      colorMap: Record<number, number[]>,
      isNumbering: boolean
    ) => {
      const segmentations = Array.isArray(finding?.segmentation[0])
        ? finding.segmentation
        : [finding.segmentation];

      segmentations.forEach((points: number[]) => {
        if (points?.length < 2) return;

        ctx.beginPath();
        const allPoints: { x: number; y: number }[] = [];
        for (let i = 0; i < points.length; i += 2) {
          const convertedPoint = convertCoordinates(points[i], points[i + 1]);
          allPoints.push(convertedPoint);
          if (i === 0) ctx.moveTo(convertedPoint.x, convertedPoint.y);
          else ctx.lineTo(convertedPoint.x, convertedPoint.y);
        }
        ctx.closePath();

        const categoryId = Number(finding.category_id);
        const color = colorMap[categoryId] || [255, 0, 0, 0.5];
        const isManual = finding.annotation_source === "Manual";

        if (isNumbering) {
          // Enhanced: Responsive styling for teeth_numbering across all grid boxes
          ctx.fillStyle = "transparent";
          ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
          // Enhanced: Consistent line width scaling for all grid boxes
          const baseLineWidth = Math.max(2, Math.min(3, canvas.width / 120));
          ctx.lineWidth = isManual ? baseLineWidth * 1.2 : baseLineWidth;
          ctx.setLineDash([]);
          ctx.stroke();

          if (allPoints.length > 0) {
            const centerX = allPoints.reduce((acc, pt) => acc + pt.x, 0) / allPoints.length;
            const centerY = allPoints.reduce((acc, pt) => acc + pt.y, 0) / allPoints.length;

            // Enhanced: Consistent font sizing for all grid boxes
            const baseFontSize = Math.max(14, Math.min(18, canvas.width / 20));
            ctx.fillStyle = "#000000";
            ctx.font = `bold ${baseFontSize}px Arial`;
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";

            // Enhanced: Ensure text is always visible with proper contrast
            ctx.shadowColor = "rgba(255, 255, 255, 0.8)";
            ctx.shadowBlur = 2;
            ctx.fillText(`${categoryId}`, centerX, centerY);
            ctx.shadowBlur = 0; // Reset shadow

            // Removed: AI/M badges for teeth_numbering annotations as requested
          }
        } else {
          // Enhanced: Different styling for manual vs AI annotations in multi-image layouts
          if (isManual) {
            // Manual annotations: Only outline, no fill
            ctx.fillStyle = "transparent";
            ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
            const baseLineWidth = Math.max(2, Math.min(3, canvas.width / 120));
            ctx.lineWidth = baseLineWidth * 1.2;
            ctx.setLineDash([]);
            ctx.stroke();
          } else {
            // AI annotations: Keep original fill + outline
            const fillOpacity = 0.3;
            ctx.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${fillOpacity})`;
            ctx.fill();
            ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
            const baseLineWidth = Math.max(2, Math.min(3, canvas.width / 120));
            ctx.lineWidth = baseLineWidth;
            ctx.setLineDash([]);
            ctx.stroke();
          }

          // Enhanced: Add AI/M badges for teeth_decay annotations (no category labels)
          if (allPoints.length > 0) {
            const lowestPoint = allPoints.reduce(
              (lowest, point) => (point.y > lowest.y ? point : lowest),
              allPoints[0]
            );

            const badgeText = isManual ? "M" : "AI";
            const badgeWidth = Math.max(16, Math.min(24, canvas.width / 30));
            const badgeHeight = Math.max(12, Math.min(18, canvas.width / 35));
            const badgePadding = 3;

            // Position badge below the lowest point of the polygon
            const badgeX = Math.max(
              0,
              Math.min(
                lowestPoint.x - badgeWidth / 2,
                canvas.width - badgeWidth
              )
            );
            const badgeY = Math.max(
              0,
              Math.min(
                lowestPoint.y + badgePadding,
                canvas.height - badgeHeight
              )
            );

            // Draw badge background
            ctx.fillStyle = isManual ? "#FF6347" : "#00BFFF";
            ctx.fillRect(badgeX, badgeY, badgeWidth, badgeHeight);

            // Draw badge text
            ctx.fillStyle = "white";
            const badgeFontSize = Math.max(8, Math.min(12, canvas.width / 40));
            ctx.font = `bold ${badgeFontSize}px Arial`;
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(
              badgeText,
              badgeX + badgeWidth / 2,
              badgeY + badgeHeight / 2
            );
          }
        }
      });
    };

    // Draw teeth numbering annotations if enabled
    if (showTeethNumbering && imageAnnotations.numbering) {
      imageAnnotations.numbering.forEach((annotation) => {
        drawPolygonForImage(annotation, randomColors.TeethNumbering, true);
      });
    }

    // Draw teeth decay annotations if enabled
    if (showTeethDecay && imageAnnotations.decay) {
      imageAnnotations.decay.forEach((annotation) => {
        drawPolygonForImage(annotation, randomColors.TeethDecay, false);
      });
    }
  }, [globalAnnotations, showTeethNumbering, showTeethDecay]);

  //////// end ///////////
  const loadImage = useCallback(() => {
    try {
      const imageUrl = getImageUrl();
      if (!imageUrl) {
        setImageError(true);
        setImageLoaded(false);
        return;
      }

      const img = new Image();
      img.crossOrigin = "anonymous";

      img.onload = () => {
        try {
          // Memory optimization: Check image size to prevent memory issues
          const maxImageSize = 4096; // Maximum dimension
          if (img.width > maxImageSize || img.height > maxImageSize) {
            console.warn(`Image size (${img.width}x${img.height}) exceeds safe limit (${maxImageSize}x${maxImageSize})`);
            // Still proceed but log warning
          }

          imageRef.current = img;
          setImageLoaded(true);
          setImageError(false);
          setImageDimensions({ width: img.width, height: img.height });
          console.log("Image Dimensions:", {
            width: img.width,
            height: img.height,
          });
        } catch (error) {
          console.error("Error processing loaded image:", error);
          setImageError(true);
          setImageLoaded(false);
        }
      };

      img.onerror = (error) => {
        console.error("Failed to load image:", imageUrl, error);
        setImageError(true);
        setImageLoaded(false);
      };

      img.src = imageUrl;
    } catch (error) {
      console.error("Error in loadImage:", error);
      setImageError(true);
      setImageLoaded(false);
    }
  }, [getImageUrl]);

  const saveAnnotations = useCallback(() => {
    // Don't save to localStorage if we just submitted feedback
    if (justSubmittedFeedback) {
      console.log("Skipping save to localStorage - just submitted feedback");
      return;
    }

    const currentImageId = getImageId();

    // Enhanced: Save complete per-image state including history
    const perImageData: PerImageAnnotationData = {
      manual: currentAnnotations,
      stored: storedAnnotations || { numbering: [], decay: [] },
      history: history,
      historyIndex: historyIndex,
      lastModified: Date.now()
    };

    annotationStorage.set(currentImageId, perImageData);

    // Also update the per-image state map
    updatePerImageState(currentImageId, {
      annotations: currentAnnotations,
      history: history,
      historyIndex: historyIndex,
      storedAnnotations: storedAnnotations || { numbering: [], decay: [] },
      isEditingAnnotation: isEditingAnnotation,
      selectedAnnotationId: selectedAnnotationId,
      draggedPointIndex: draggedPointIndex
    });
  }, [currentAnnotations, storedAnnotations, history, historyIndex, justSubmittedFeedback, isEditingAnnotation, selectedAnnotationId, draggedPointIndex]);

  const loadAnnotations = useCallback(() => {
    const currentImageId = getImageId();
    const saved = annotationStorage.get(currentImageId);

    if (saved && saved.manual && saved.manual.length > 0) {
      // Enhanced: Load complete per-image state including history (only if annotations exist)
      setCurrentAnnotations(saved.manual);
      setStoredAnnotations(saved.stored || { numbering: [], decay: [] });
      setHistory(saved.history || [saved.manual]);
      setHistoryIndex(saved.historyIndex || 0);

      // Update per-image state map
      updatePerImageState(currentImageId, {
        annotations: saved.manual,
        history: saved.history || [saved.manual],
        historyIndex: saved.historyIndex || 0,
        storedAnnotations: saved.stored || { numbering: [], decay: [] },
        isEditingAnnotation: false,
        selectedAnnotationId: null,
        draggedPointIndex: null
      });

      console.log(`Loaded annotations for image ${currentImageId}:`, saved);
    } else {
      // Create fresh state for new image or when annotations were erased
      const freshState = {
        annotations: [],
        history: [[]],
        historyIndex: 0,
        storedAnnotations: saved?.stored || annotations || { numbering: [], decay: [] },
        isEditingAnnotation: false,
        selectedAnnotationId: null,
        draggedPointIndex: null
      };

      setCurrentAnnotations(freshState.annotations);
      setStoredAnnotations(freshState.storedAnnotations);
      setHistory(freshState.history);
      setHistoryIndex(freshState.historyIndex);

      updatePerImageState(currentImageId, freshState);
      console.log(`Created fresh state for image ${currentImageId} (no annotations or erased)`);
    }

    setShowTeethDecay(true);
    setShowTeethNumbering(false);
  }, [annotations]);

  useEffect(() => {
    if (isOpen) {
      const currentImageId = getImageId();
      setImageId(currentImageId);
      loadImage();

      // Enhanced: Switch to the current image's state
      switchToImageState(currentImageId);
      loadAnnotations();

      // Enhanced: Ensure annotations are loaded after a short delay to handle timing issues
      setTimeout(() => {
        loadAnnotations();
      }, 100);
    }
  }, [isOpen]); // Minimal dependencies to prevent infinite loops

  useEffect(() => {
    if (!isOpen) {
      saveAnnotations();
      imageRef.current = null;
      setImageLoaded(false);
      setImageError(false);
      setImageDimensions(null);
      setHistory([[]]);
      setHistoryIndex(0);
      setIsEditingAnnotation(false);
      setSelectedAnnotationId(null);
      setDraggedPointIndex(null);
    }
  }, [isOpen, saveAnnotations]);

  useEffect(() => {
    if (!isOpen || !imageLoaded) return;

    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const updateCanvasSize = () => {
      if (activeImageCount === 1) {
        // For Button 1 layout, use consistent dimensions based on image aspect ratio
        if (imageDimensions) {
          setButton1CanvasDimensions(canvas, imageDimensions.width, imageDimensions.height);
        } else {
          setButton1CanvasDimensions(canvas);
        }
      } else {
        // For multi-image layouts, use dynamic sizing
        const containerRect = container.getBoundingClientRect();
        canvas.width = Math.min(800, containerRect.width - 40);
        canvas.height = Math.min(600, containerRect.height - 40);
      }
      drawCanvas();
    };

    updateCanvasSize();
    const resizeObserver = new ResizeObserver(updateCanvasSize);
    resizeObserver.observe(container);

    return () => resizeObserver.unobserve(container);
  }, [isOpen, imageLoaded, activeImageCount, setButton1CanvasDimensions]);

  useEffect(() => {
    if (highlightedFinding) {
      const finding = aiFindings.find((f) => f.id === highlightedFinding);
      if (finding) setSelectedFinding(finding);
    }
  }, [highlightedFinding, aiFindings]);

  // useEffect(() => {
  //   if (imageLoaded) drawCanvas();
  // }, [
  //   imageLoaded,
  //   showTeethNumbering,
  //   showTeethDecay,
  //   currentAnnotations,
  //   zoom,
  //   rotation,
  //   panOffset,
  //   brightness,
  //   contrast,
  //   showAIFindings,
  //   highlightedFinding,
  //   drawCanvas,
  //   isEditingAnnotation,
  //   selectedAnnotationId,
  // ]);

  useEffect(() => {
    // Fixed: Ensure proper annotation alignment on first teeth_numbering activation
    if (activeImageCount === 1) {
      if (imageLoaded) {
        // Add small delay to ensure all state updates are complete
        setTimeout(() => {
          drawCanvas();
        }, 50);
      }
    } else if (selectedImages.length > 1 && !isLoadingMultipleImages) {
      // Fixed: Stable multi-image drawing with proper annotation alignment
      setTimeout(() => {
        debouncedDrawMultipleImages();
      }, 100); // Increased delay for stable rendering
    }

    // Cleanup function to clear any pending timeouts
    return () => {
      if (drawDebounceRef.current) {
        clearTimeout(drawDebounceRef.current);
      }
    };
  }, [
    imageLoaded,
    showTeethNumbering,
    showTeethDecay,
    currentAnnotations,
    storedAnnotations,
    zoom,
    rotation,
    panOffset,
    brightness,
    contrast,
    showAIFindings,
    highlightedFinding,
    // Removed drawCanvas from dependencies to prevent infinite loop
    isEditingAnnotation,
    selectedAnnotationId,
    activeImageCount,
    selectedImages,
    isLoadingMultipleImages,
  ]);

  const previousVisitsImages = useSelector(
    (state: RootState) => state.previousVisit.previousVisitLoadedXraysList
  );

  // Initialize available images for full-screen mode
  useEffect(() => {
    if (isFullScreen && allImages) {
      const images: Array<{ slotId: string, image: XrayImage }> = [];

      // Add panoramic image if available
      if (allImages.panoramicImage) {
        images.push({ slotId: 'PANORAMIC', image: allImages.panoramicImage });
      }

      // Add all slot images
      Object.entries(allImages.slots).forEach(([slotId, img]) => {
        if (img) {
          images.push({ slotId, image: img });
        }
      });


      setAllAvailableImages(images);

      // Set the current image as the first selected image
      if (image && typeof image === 'object') {
        setSelectedImages([{ slotId: slotInfo?.id || 'current', image }]);
        setCurrentMainImage(image);
      }
    }
  }, [isFullScreen, allImages, image, slotInfo]);

  // Initialize currentMainImage when component mounts
  useEffect(() => {
    if (image && !currentMainImage) {
      console.log("Initializing with default image:", typeof image === 'object' ? image.imageId : image);
      setCurrentMainImage(image);
      // Reset layout switch flag on fresh initialization
      layoutSwitchProcessedRef.current = false;
    }
  }, [image, currentMainImage]);

  // Reset state when component opens (isOpen changes from false to true)
  useEffect(() => {
    if (isOpen) {
      console.log("AI-annotator opened, ensuring proper initialization");
      // Reset layout switch flag to allow proper initialization
      layoutSwitchProcessedRef.current = false;

      // If no current main image is set, initialize with the default
      if (!currentMainImage && image) {
        setCurrentMainImage(image);
      }
    }
  }, [isOpen, currentMainImage, image]);

  // Enhanced: Reload image when currentMainImage changes
  useEffect(() => {
    if (currentMainImage && activeImageCount === 1) {
      console.log("Current main image changed, reloading:", currentMainImage);
      loadImage();
    }
  }, [currentMainImage, activeImageCount, loadImage]);

  // Enhanced: Force redraw when image is loaded
  useEffect(() => {
    if (imageLoaded && activeImageCount === 1) {
      // Force a redraw when the image is loaded
      setTimeout(() => {
        drawCanvas();
      }, 100);
    }
  }, [imageLoaded, activeImageCount, drawCanvas]);

  // Ensure proper canvas sizing when image loads for perfect fit
  useEffect(() => {
    if (imageLoaded && canvasRef.current && imageDimensions && activeImageCount === 1) {
      const canvas = canvasRef.current;

      // For Button 1 layout, use consistent dimensions based on image aspect ratio
      setButton1CanvasDimensions(canvas, imageDimensions.width, imageDimensions.height);

      // Enhanced: Ensure manual annotations are loaded for Button 1 layout
      const currentImageId = getImageId();
      const savedManualAnnotations = annotationStorage.get(currentImageId);
      if (savedManualAnnotations && savedManualAnnotations.manual.length > 0) {
        setCurrentAnnotations(savedManualAnnotations.manual);
        setHistory([savedManualAnnotations.manual]);
        setHistoryIndex(0);
      }

      // Trigger redraw after sizing and annotation loading
      setTimeout(() => {
        drawCanvas();
      }, 10);
    }
  }, [imageLoaded, imageDimensions, activeImageCount, setButton1CanvasDimensions, getImageId]);

  // Enhanced: Ensure manual annotations are loaded when switching to Button 1 layout
  useEffect(() => {
    if (activeImageCount === 1 && imageLoaded) {
      // Load manual annotations for the current image when in Button 1 layout
      const currentImageId = getImageId();
      const savedManualAnnotations = annotationStorage.get(currentImageId);

      if (savedManualAnnotations && savedManualAnnotations.manual && savedManualAnnotations.manual.length > 0) {
        // Don't restore annotations if we just submitted feedback
        if (justSubmittedFeedback) {
          console.log("Skipping annotation restoration - just submitted feedback");
          return;
        }

        // Only update if current annotations are empty or different to prevent loops
        // if (currentAnnotations.length === 0 ||
        //     JSON.stringify(currentAnnotations) !== JSON.stringify(savedManualAnnotations.manual)) {
        //   console.log("Restoring annotations from localStorage (useEffect 1)");
        //   setCurrentAnnotations(savedManualAnnotations.manual);
        //   setHistory([savedManualAnnotations.manual]);
        //   setHistoryIndex(0);

        //   // Trigger redraw to show the loaded annotations
        //   setTimeout(() => {
        //     drawCanvas();
        //   }, 50);
        // }
      }
    }
  }, [activeImageCount, imageLoaded, justSubmittedFeedback]); // Added justSubmittedFeedback to dependencies

  // Enhanced: Reload annotations when image changes (for refresh/navigation scenarios)
  useEffect(() => {
    if (imageLoaded && image) {
      const currentImageId = getImageId();

      // Force reload annotations from localStorage (persistent storage)
      const savedManualAnnotations = annotationStorage.get(currentImageId);
      if (savedManualAnnotations && savedManualAnnotations.manual && savedManualAnnotations.manual.length > 0) {
        // Don't restore annotations if we just submitted feedback
        if (justSubmittedFeedback) {
          console.log("Skipping annotation restoration - just submitted feedback (useEffect 2)");
          return;
        }

        // Only update if annotations are different to prevent loops
        if (JSON.stringify(currentAnnotations) !== JSON.stringify(savedManualAnnotations.manual)) {
          console.log("Restoring annotations from localStorage (useEffect 2)");
          setCurrentAnnotations(savedManualAnnotations.manual);
          setHistory([savedManualAnnotations.manual]);
          setHistoryIndex(0);

          // Trigger redraw after loading
          setTimeout(() => {
            drawCanvas();
          }, 100);
        }
      }
    }
  }, [image, imageLoaded, justSubmittedFeedback]); // Added justSubmittedFeedback to dependencies

  // Note: Auto-save is handled in specific places (drawing completion, etc.) to avoid infinite loops

  // Enhanced: Load manual annotations from localStorage on component mount (for page refresh)
  useEffect(() => {
    // Only run once when component mounts and image is available
    if (image) {
      const currentImageId = getImageId();
      if (currentImageId && currentImageId !== "unknown") {
        const saved = annotationStorage.get(currentImageId);
        if (saved && saved.manual && saved.manual.length > 0) {
          // Don't restore annotations if we just submitted feedback
          if (justSubmittedFeedback) {
            console.log("Skipping annotation restoration - just submitted feedback (useEffect 3)");
            return;
          }

          console.log("Restoring annotations from localStorage (useEffect 3)");
          setCurrentAnnotations(saved.manual);
          setHistory([saved.manual]);
          setHistoryIndex(0);
        }
      }
    }
  }, [image, justSubmittedFeedback]); // Added justSubmittedFeedback to dependencies



  const samplePoints = (
    points: { x: number; y: number }[],
    minDistance: number
  ) => {
    if (points.length <= 2) return points;
    const sampled: { x: number; y: number }[] = [points[0]];
    let lastPoint = points[0];

    for (let i = 1; i < points.length; i++) {
      const point = points[i];
      const distance = Math.sqrt(
        Math.pow(point.x - lastPoint.x, 2) + Math.pow(point.y - lastPoint.y, 2)
      );
      if (distance >= minDistance) {
        sampled.push(point);
        lastPoint = point;
      }
    }
    // Ensure the shape is closed by adding the first point
    if (sampled.length > 1 && sampled[0] !== points[0]) {
      sampled.push(sampled[0]);
    }
    return sampled;
  };

  const transformMouseCoordinates = (x: number, y: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x, y };

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    let transformedX = x - centerX - panOffset.x;
    let transformedY = y - centerY - panOffset.y;

    const cosTheta = Math.cos((-rotation * Math.PI) / 180);
    const sinTheta = Math.sin((-rotation * Math.PI) / 180);

    const rotatedX = transformedX * cosTheta - transformedY * sinTheta;
    const rotatedY = transformedX * sinTheta + transformedY * cosTheta;

    return {
      x: rotatedX / zoom + centerX,
      y: rotatedY / zoom + centerY,
    };
  };

  const findNearestAnnotationPoint = (x: number, y: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return null;

    const MIN_DISTANCE = 15 / zoom;

    for (const annotation of currentAnnotations) {
      if (annotation.type === "freehand" && annotation.points) {
        const sampledPoints = annotation.points.filter(
          (_, index) => index % 5 === 0
        );
        for (let i = 0; i < sampledPoints?.length; i++) {
          const point = sampledPoints[i];
          const originalIndex = i * 5;
          const clamped = clampCoordinates(
            point.x,
            point.y,
            canvas.width,
            canvas.height
          );
          const distance = Math.sqrt(
            Math.pow(clamped.x - x, 2) + Math.pow(clamped.y - y, 2)
          );
          if (distance < MIN_DISTANCE) {
            return { annotationId: annotation.id, pointIndex: originalIndex };
          }
        }
      }
    }
    return null;
  };

  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = e.currentTarget as HTMLCanvasElement;
      if (!canvas || !imageLoaded) return;

      // Enhanced: Disable drawing in multi-image layouts (Button 2, 4, 8)
      if (activeImageCount > 1) {
        return; // Prevent drawing in multi-image modes
      }

      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      const { x, y } = transformMouseCoordinates(mouseX, mouseY);
      const clamped = clampCoordinates(x, y, canvas.width, canvas.height);



      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
        longPressTimeoutRef.current = null;
      }

      // Only handle drawing if a tool is selected (not null)
      if (currentTool === null) {
        return;
      }

      if (currentTool === "pen" && !isEditingAnnotation) {
        longPressTimeoutRef.current = setTimeout(() => {
          const nearest = findNearestAnnotationPoint(clamped.x, clamped.y);
          if (nearest) {
            setIsEditingAnnotation(true);
            setSelectedAnnotationId(nearest.annotationId);
            setDraggedPointIndex(nearest.pointIndex);
          }
        }, 500);
      }

      if (!isEditingAnnotation && currentTool === "move") {
        setIsPanning(true);
        setLastMousePos({ x: mouseX, y: mouseY });
      } else if (!isEditingAnnotation && currentTool && currentTool !== null) {
        setIsDrawing(true);

        let newAnnotation: ManualAnnotation;
        const decayValue = Number(selectedDecayType);
        const color = getAnnotationColor(selectedDecayType);

        if (currentTool === "pen") {
          newAnnotation = {
            id: `annotation-${Date.now()}`,
            type: "freehand",
            points: [{ x: clamped.x, y: clamped.y }],
            color,
            label: currentAnnotationType,
            category_id: decayValue,
            segmentation: [],
            annotation_source: "Manual", // Enhanced: Add annotation source for consistent rendering
          };
        } else if (currentTool === "rectangle") {
          newAnnotation = {
            id: `annotation-${Date.now()}`,
            type: "rectangle",
            coordinates: { x: clamped.x, y: clamped.y, width: 0, height: 0 },
            color,
            label: currentAnnotationType,
            category_id: decayValue,
            segmentation: [],
            annotation_source: "Manual", // Enhanced: Add annotation source for consistent rendering
          };
        } else if (currentTool === "circle") {
          newAnnotation = {
            id: `annotation-${Date.now()}`,
            type: "circle",
            coordinates: { x: clamped.x, y: clamped.y, radius: 0 },
            color,
            label: currentAnnotationType,
            category_id: decayValue,
            segmentation: [],
            annotation_source: "Manual", // Enhanced: Add annotation source for consistent rendering
          };
        } else if (currentTool === "eraser" && !isEraserProcessing) {
          setIsEraserProcessing(true);

          const currentImageId = getImageId();

          setCurrentAnnotations((prev) => {
            const newAnnotations = prev.filter((annotation) => {
              if (annotation.type === "freehand" && annotation.points) {
                return !annotation.points.some(
                  (point) =>
                    Math.sqrt(
                      Math.pow(point.x - clamped.x, 2) +
                      Math.pow(point.y - clamped.y, 2)
                    ) < 15
                );
              } else if (
                annotation.type === "rectangle" &&
                annotation.coordinates
              ) {
                const {
                  x: rectX,
                  y: rectY,
                  width = 0,
                  height = 0,
                } = annotation.coordinates;
                return !(
                  clamped.x >= rectX &&
                  clamped.x <= rectX + width &&
                  clamped.y >= rectY &&
                  clamped.y <= rectY + height
                );
              } else if (
                annotation.type === "circle" &&
                annotation.coordinates
              ) {
                const {
                  x: circleX,
                  y: circleY,
                  radius = 0,
                } = annotation.coordinates;
                return (
                  Math.sqrt(
                    Math.pow(clamped.x - circleX, 2) +
                    Math.pow(clamped.y - circleY, 2)
                  ) > radius
                );
              }
              return true;
            });

            const newHistory = [...history.slice(0, historyIndex + 1), newAnnotations];
            const newHistoryIndex = historyIndex + 1;

            setHistory(newHistory);
            setHistoryIndex(newHistoryIndex);

            // Enhanced: Update per-image state for current image
            updatePerImageState(currentImageId, {
              annotations: newAnnotations,
              history: newHistory,
              historyIndex: newHistoryIndex
            });

            // Enhanced: Immediately save the erased state to localStorage
            const perImageData: PerImageAnnotationData = {
              manual: newAnnotations,
              stored: storedAnnotations || { numbering: [], decay: [] },
              history: newHistory,
              historyIndex: newHistoryIndex,
              lastModified: Date.now()
            };
            annotationStorage.set(currentImageId, perImageData);

            console.log(`Eraser applied to image ${currentImageId}, removed ${prev.length - newAnnotations.length} annotations, saved to localStorage`);
            return newAnnotations;
          });

          // Reset eraser processing flag after a short delay
          setTimeout(() => {
            setIsEraserProcessing(false);
          }, 200);
          return;
        } else {
          return;
        }

        setCurrentAnnotations((prev) => {
          const newAnnotations = [...prev, newAnnotation];
          setHistory((prev) => [
            ...prev.slice(0, historyIndex + 1),
            newAnnotations,
          ]);
          setHistoryIndex((prev) => prev + 1);
          return newAnnotations;
        });
      }
    },
    [
      currentTool,
      currentAnnotationType,
      selectedDecayType,
      historyIndex,
      imageLoaded,
      isEditingAnnotation,
    ]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas || !imageLoaded) return;

      // Enhanced: Disable drawing in multi-image layouts (Button 2, 4, 8)
      if (activeImageCount > 1) {
        return; // Prevent drawing in multi-image modes
      }

      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      const { x, y } = transformMouseCoordinates(mouseX, mouseY);
      const clamped = clampCoordinates(x, y, canvas.width, canvas.height);

      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const imageWidth = imageDimensions?.width || canvasWidth;
      const imageHeight = imageDimensions?.height || canvasHeight;

      if (
        isEditingAnnotation &&
        selectedAnnotationId !== null &&
        draggedPointIndex !== null
      ) {
        setCurrentAnnotations((prev) => {
          const newAnnotations = [...prev];
          const annotation = newAnnotations.find(
            (ann) => ann.id === selectedAnnotationId
          );
          if (
            annotation &&
            annotation.type === "freehand" &&
            annotation.points
          ) {
            annotation.points[draggedPointIndex] = {
              x: clamped.x,
              y: clamped.y,
            };
            const sampledPoints = samplePoints(annotation.points, 5 / zoom);
            annotation.segmentation = sampledPoints.reduce(
              (acc: number[], point) => {
                const scaled = denormalizeCoordinates(
                  point,
                  canvasWidth,
                  canvasHeight,
                  imageWidth,
                  imageHeight
                );
                return [...acc, scaled.x, scaled.y];
              },
              []
            );
          }
          setHistory((prev) => [
            ...prev.slice(0, historyIndex + 1),
            newAnnotations,
          ]);
          setHistoryIndex((prev) => prev + 1);
          return newAnnotations;
        });
      } else if (isPanning && lastMousePos && currentTool === "move") {
        const dx = mouseX - lastMousePos.x;
        const dy = mouseY - lastMousePos.y;
        if (debounceTimeoutRef.current)
          clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = setTimeout(() => {
          setPanOffset((prev) => ({
            x: prev.x + dx,
            y: prev.y + dy,
          }));
          setLastMousePos({ x: mouseX, y: mouseY });
        }, 10);
      } else if (isDrawing && currentTool && currentTool !== "move") {
        setCurrentAnnotations((prev) => {
          if (!prev?.length) return prev;
          const newAnnotations = [...prev];
          const lastAnnotation = newAnnotations[newAnnotations?.length - 1];
          if (!lastAnnotation) return newAnnotations;

          if (currentTool === "pen" && lastAnnotation.type === "freehand") {
            const lastPoint =
              lastAnnotation.points?.[lastAnnotation.points?.length - 1];
            if (lastPoint) {
              const distance = Math.sqrt(
                Math.pow(clamped.x - lastPoint.x, 2) +
                Math.pow(clamped.y - lastPoint.y, 2)
              );
              if (distance > 5 / zoom) {
                lastAnnotation.points = [
                  ...(lastAnnotation.points || []),
                  { x: clamped.x, y: clamped.y },
                ];
                const sampledPoints = samplePoints(
                  lastAnnotation.points,
                  5 / zoom
                );
                lastAnnotation.segmentation = sampledPoints.reduce(
                  (acc: number[], point) => {
                    const scaled = denormalizeCoordinates(
                      point,
                      canvasWidth,
                      canvasHeight,
                      imageWidth,
                      imageHeight
                    );
                    return [...acc, scaled.x, scaled.y];
                  },
                  []
                );
              }
            } else {
              lastAnnotation.points = [
                ...(lastAnnotation.points || []),
                { x: clamped.x, y: clamped.y },
              ];
              const sampledPoints = samplePoints(
                lastAnnotation.points,
                5 / zoom
              );
              lastAnnotation.segmentation = sampledPoints.reduce(
                (acc: number[], point) => {
                  const scaled = denormalizeCoordinates(
                    point,
                    canvasWidth,
                    canvasHeight,
                    imageWidth,
                    imageHeight
                  );
                  return [...acc, scaled.x, scaled.y];
                },
                []
              );
            }
          }
          return newAnnotations;
        });
      }
    },
    [
      isDrawing,
      isPanning,
      lastMousePos,
      currentTool,
      zoom,
      rotation,
      panOffset,
      imageLoaded,
      isEditingAnnotation,
      selectedAnnotationId,
      draggedPointIndex,
      historyIndex,
      imageDimensions,
    ]
  );

  const handleMouseUp = useCallback(() => {
    // Enhanced: Disable drawing in multi-image layouts (Button 2, 4, 8)
    if (activeImageCount > 1) {
      return; // Prevent drawing in multi-image modes
    }

    if (longPressTimeoutRef.current) {
      clearTimeout(longPressTimeoutRef.current);
      longPressTimeoutRef.current = null;
    }

    if (isEditingAnnotation) {
      setIsEditingAnnotation(false);
      setSelectedAnnotationId(null);
      setDraggedPointIndex(null);
    } else if (isDrawing) {
      setIsDrawing(false);
      setCurrentAnnotations((prev) => {
        if (!prev?.length) return prev;
        const newAnnotations = [...prev];
        const lastAnnotation = newAnnotations[newAnnotations?.length - 1];
        if (!lastAnnotation) return newAnnotations;

        let segmentation: number[] = [];
        const canvas = canvasRef.current;
        const canvasWidth = canvas?.width || 800;
        const canvasHeight = canvas?.height || 600;
        const imageWidth = imageDimensions?.width || canvasWidth;
        const imageHeight = imageDimensions?.height || canvasHeight;

        if (lastAnnotation.type === "freehand" && lastAnnotation.points) {
          // Enhanced: Ensure polygon closure for better shape definition
          if (lastAnnotation.points?.length > 2) {
            const startPoint = lastAnnotation.points[0];
            const lastPoint = lastAnnotation.points[lastAnnotation.points.length - 1];

            // Only add start point if it's not already close to the last point
            const distance = Math.sqrt(
              Math.pow(startPoint.x - lastPoint.x, 2) +
              Math.pow(startPoint.y - lastPoint.y, 2)
            );

            if (distance > 10) {
              lastAnnotation.points = [...lastAnnotation.points, startPoint];
            }
          }

          // Enhanced: Consistent segmentation calculation with proper sampling
          const sampledPoints = samplePoints(lastAnnotation.points, 5 / zoom);
          segmentation = sampledPoints.reduce((acc: number[], point) => {
            const scaled = denormalizeCoordinates(
              point,
              canvasWidth,
              canvasHeight,
              imageWidth,
              imageHeight
            );
            return [...acc, scaled.x, scaled.y];
          }, []);
        }

        // Enhanced: Update segmentation and finalize annotation
        lastAnnotation.segmentation = segmentation;
        newAnnotations[newAnnotations?.length - 1] = lastAnnotation;

        // Enhanced: Update history only when drawing is completed
        setHistory((prev) => [
          ...prev.slice(0, historyIndex + 1),
          newAnnotations,
        ]);
        setHistoryIndex((prev) => prev + 1);

        // Enhanced: Save annotations after drawing completion
        setTimeout(() => {
          saveAnnotations();
        }, 50);

        return newAnnotations;
      });
    }
    if (isPanning) {
      setIsPanning(false);
      setLastMousePos(null);
    }
  }, [isDrawing, isPanning, isEditingAnnotation, imageDimensions, zoom]);

  const handleWheel = useCallback(
    (e: WheelEvent) => {
      e.preventDefault();
      const canvas = canvasRef.current;
      if (!canvas || !imageLoaded) return;

      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
      const newZoom = Math.max(0.1, Math.min(zoom * zoomFactor, 3));

      const mouseCanvasX = (mouseX - canvas.width / 2 - panOffset.x) / zoom;
      const mouseCanvasY = (mouseY - canvas.height / 2 - panOffset.y) / zoom;
      const newPanX = panOffset.x - mouseCanvasX * (newZoom - zoom);
      const newPanY = panOffset.y - mouseCanvasY * (newZoom - zoom);

      setZoom(newZoom);
      setPanOffset({ x: newPanX, y: newPanY });
    },
    [zoom, panOffset, imageLoaded]
  );

  const handleTouchStart = useCallback(
    (e: React.TouchEvent<HTMLCanvasElement>) => {
      e.preventDefault();
      const canvas = canvasRef.current;
      if (!canvas || !imageLoaded) return;

      const rect = canvas.getBoundingClientRect();
      const touches = e.touches;

      if (touches?.length === 1 && currentTool && !isEditingAnnotation) {
        const touchX = touches[0].clientX - rect.left;
        const touchY = touches[0].clientY - rect.top;
        const { x, y } = transformMouseCoordinates(touchX, touchY);
        const clamped = clampCoordinates(x, y, canvas.width, canvas.height);

        if (currentTool === "pen") {
          longPressTimeoutRef.current = setTimeout(() => {
            const nearest = findNearestAnnotationPoint(clamped.x, clamped.y);
            if (nearest) {
              setIsEditingAnnotation(true);
              setSelectedAnnotationId(nearest.annotationId);
              setDraggedPointIndex(nearest.pointIndex);
            }
          }, 500);
        }

        if (currentTool === "move") {
          setIsPanning(true);
          setLastMousePos({ x: touchX, y: touchY });
        } else if (!isEditingAnnotation) {
          setIsDrawing(true);
          let newAnnotation: ManualAnnotation;
          const decayValue = Number(selectedDecayType);
          const color = getAnnotationColor(selectedDecayType);

          if (currentTool === "pen") {
            newAnnotation = {
              id: `annotation-${Date.now()}`,
              type: "freehand",
              points: [{ x: clamped.x, y: clamped.y }],
              color,
              label: currentAnnotationType,
              category_id: decayValue,
              segmentation: [],
              annotation_source: "Manual", // Enhanced: Add annotation source for consistent rendering
            };
          } else if (currentTool === "rectangle") {
            newAnnotation = {
              id: `annotation-${Date.now()}`,
              type: "rectangle",
              coordinates: { x: clamped.x, y: clamped.y, width: 0, height: 0 },
              color,
              label: currentAnnotationType,
              category_id: decayValue,
              segmentation: [],
              annotation_source: "Manual", // Enhanced: Add annotation source for consistent rendering
            };
          } else if (currentTool === "circle") {
            newAnnotation = {
              id: `annotation-${Date.now()}`,
              type: "circle",
              coordinates: { x: clamped.x, y: clamped.y, radius: 0 },
              color,
              label: currentAnnotationType,
              category_id: decayValue,
              segmentation: [],
              annotation_source: "Manual", // Enhanced: Add annotation source for consistent rendering
            };
          } else if (currentTool === "eraser" && !isEraserProcessing) {
            setIsEraserProcessing(true);

            const currentImageId = getImageId();

            setCurrentAnnotations((prev) => {
              const newAnnotations = prev.filter((annotation) => {
                if (annotation.type === "freehand" && annotation.points) {
                  return !annotation.points.some(
                    (point) =>
                      Math.sqrt(
                        Math.pow(point.x - clamped.x, 2) +
                        Math.pow(point.y - clamped.y, 2)
                      ) < 15
                  );
                } else if (
                  annotation.type === "rectangle" &&
                  annotation.coordinates
                ) {
                  const {
                    x: rectX,
                    y: rectY,
                    width = 0,
                    height = 0,
                  } = annotation.coordinates;
                  return !(
                    clamped.x >= rectX &&
                    clamped.x <= rectX + width &&
                    clamped.y >= rectY &&
                    clamped.y <= rectY + height
                  );
                } else if (
                  annotation.type === "circle" &&
                  annotation.coordinates
                ) {
                  const {
                    x: circleX,
                    y: circleY,
                    radius = 0,
                  } = annotation.coordinates;
                  return (
                    Math.sqrt(
                      Math.pow(clamped.x - circleX, 2) +
                      Math.pow(clamped.y - circleY, 2)
                    ) > radius
                  );
                }
                return true;
              });

              const newHistory = [...history.slice(0, historyIndex + 1), newAnnotations];
              const newHistoryIndex = historyIndex + 1;

              setHistory(newHistory);
              setHistoryIndex(newHistoryIndex);

              // Enhanced: Update per-image state for current image
              updatePerImageState(currentImageId, {
                annotations: newAnnotations,
                history: newHistory,
                historyIndex: newHistoryIndex
              });

              // Enhanced: Immediately save the erased state to localStorage
              const perImageData: PerImageAnnotationData = {
                manual: newAnnotations,
                stored: storedAnnotations || { numbering: [], decay: [] },
                history: newHistory,
                historyIndex: newHistoryIndex,
                lastModified: Date.now()
              };
              annotationStorage.set(currentImageId, perImageData);

              console.log(`Eraser applied to image ${currentImageId} (touch), removed ${prev.length - newAnnotations.length} annotations, saved to localStorage`);
              return newAnnotations;
            });

            // Reset eraser processing flag after a short delay
            setTimeout(() => {
              setIsEraserProcessing(false);
            }, 200);
            return;
          } else {
            return;
          }

          setCurrentAnnotations((prev) => {
            const newAnnotations = [...prev, newAnnotation];
            setHistory((prev) => [
              ...prev.slice(0, historyIndex + 1),
              newAnnotations,
            ]);
            setHistoryIndex((prev) => prev + 1);
            return newAnnotations;
          });
        }
      } else if (touches?.length === 2) {
        const dx = touches[0].clientX - touches[1].clientX;
        const dy = touches[0].clientY - touches[1].clientY;
        touchStartDistanceRef.current = Math.sqrt(dx * dx + dy * dy);
        touchStartZoomRef.current = zoom;
      }
    },
    [
      currentTool,
      currentAnnotationType,
      selectedDecayType,
      historyIndex,
      imageLoaded,
      isEditingAnnotation,
      zoom,
    ]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent<HTMLCanvasElement>) => {
      e.preventDefault();
      const canvas = canvasRef.current;
      if (!canvas || !imageLoaded) return;

      const rect = canvas.getBoundingClientRect();
      const touches = e.touches;

      if (
        touches?.length === 1 &&
        (isPanning || isDrawing || isEditingAnnotation)
      ) {
        const touchX = touches[0].clientX - rect.left;
        const touchY = touches[0].clientY - rect.top;
        const { x, y } = transformMouseCoordinates(touchX, touchY);
        const clamped = clampCoordinates(x, y, canvas.width, canvas.height);

        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const imageWidth = imageDimensions?.width || canvasWidth;
        const imageHeight = imageDimensions?.height || canvasHeight;

        if (
          isEditingAnnotation &&
          selectedAnnotationId !== null &&
          draggedPointIndex !== null
        ) {
          setCurrentAnnotations((prev) => {
            const newAnnotations = [...prev];
            const annotation = newAnnotations.find(
              (ann) => ann.id === selectedAnnotationId
            );
            if (
              annotation &&
              annotation.type === "freehand" &&
              annotation.points
            ) {
              annotation.points[draggedPointIndex] = {
                x: clamped.x,
                y: clamped.y,
              };
              annotation.segmentation = annotation.points.reduce(
                (acc: number[], point) => {
                  const scaled = denormalizeCoordinates(
                    point,
                    canvasWidth,
                    canvasHeight,
                    imageWidth,
                    imageHeight
                  );
                  return [...acc, scaled.x, scaled.y];
                },
                []
              );
            }
            setHistory((prev) => [
              ...prev.slice(0, historyIndex + 1),
              newAnnotations,
            ]);
            setHistoryIndex((prev) => prev + 1);
            return newAnnotations;
          });
        } else if (isPanning && lastMousePos && currentTool === "move") {
          const dx = touchX - lastMousePos.x;
          const dy = touchY - lastMousePos.y;
          if (debounceTimeoutRef.current)
            clearTimeout(debounceTimeoutRef.current);
          debounceTimeoutRef.current = setTimeout(() => {
            setPanOffset((prev) => ({
              x: prev.x + dx,
              y: prev.y + dy,
            }));
            setLastMousePos({ x: touchX, y: touchY });
          }, 10);
        } else if (isDrawing && currentTool && currentTool !== "move") {
          setCurrentAnnotations((prev) => {
            if (!prev?.length) return prev;
            const newAnnotations = [...prev];
            const lastAnnotation = newAnnotations[newAnnotations?.length - 1];
            if (!lastAnnotation) return newAnnotations;

            if (currentTool === "pen" && lastAnnotation.type === "freehand") {
              const lastPoint =
                lastAnnotation.points?.[lastAnnotation.points?.length - 1];
              if (lastPoint) {
                const distance = Math.sqrt(
                  Math.pow(clamped.x - lastPoint.x, 2) +
                  Math.pow(clamped.y - lastPoint.y, 2)
                );
                if (distance > 5 / zoom) {
                  lastAnnotation.points = [
                    ...(lastAnnotation.points || []),
                    { x: clamped.x, y: clamped.y },
                  ];
                  lastAnnotation.segmentation = lastAnnotation.points.reduce(
                    (acc: number[], point) => {
                      const scaled = denormalizeCoordinates(
                        point,
                        canvasWidth,
                        canvasHeight,
                        imageWidth,
                        imageHeight
                      );
                      return [...acc, scaled.x, scaled.y];
                    },
                    []
                  );
                }
              } else {
                lastAnnotation.points = [
                  ...(lastAnnotation.points || []),
                  { x: clamped.x, y: clamped.y },
                ];
                lastAnnotation.segmentation = lastAnnotation.points.reduce(
                  (acc: number[], point) => {
                    const scaled = denormalizeCoordinates(
                      point,
                      canvasWidth,
                      canvasHeight,
                      imageWidth,
                      imageHeight
                    );
                    return [...acc, scaled.x, scaled.y];
                  },
                  []
                );
              }
            } else if (
              currentTool === "rectangle" &&
              lastAnnotation.type === "rectangle"
            ) {
              const startX = lastAnnotation.coordinates?.x || 0;
              const startY = lastAnnotation.coordinates?.y || 0;
              const width = clamped.x - startX;
              const height = clamped.y - startY;
              lastAnnotation.coordinates = {
                x: Math.min(startX, clamped.x),
                y: Math.min(startY, clamped.y),
                width: Math.abs(width),
                height: Math.abs(height),
              };
              const scaledX = Math.round(
                (Math.min(startX, clamped.x) / canvasWidth) * imageWidth
              );
              const scaledY = Math.round(
                (Math.min(startY, clamped.y) / canvasHeight) * imageHeight
              );
              const scaledWidth = Math.round(
                (Math.abs(width) / canvasWidth) * imageWidth
              );
              const scaledHeight = Math.round(
                (Math.abs(height) / canvasHeight) * imageHeight
              );
              lastAnnotation.segmentation = [
                scaledX,
                scaledY,
                scaledX + scaledWidth,
                scaledY,
                scaledX + scaledWidth,
                scaledY + scaledHeight,
                scaledX,
                scaledY + scaledHeight,
                scaledX,
                scaledY,
              ];
            } else if (
              currentTool === "circle" &&
              lastAnnotation.type === "circle"
            ) {
              const startX = lastAnnotation.coordinates?.x || 0;
              const startY = lastAnnotation.coordinates?.y || 0;
              const radius = Math.sqrt(
                Math.pow(clamped.x - startX, 2) +
                Math.pow(clamped.y - startY, 2)
              );
              lastAnnotation.coordinates = { x: startX, y: startY, radius };
              const scaledX = Math.round((startX / canvasWidth) * imageWidth);
              const scaledY = Math.round((startY / canvasHeight) * imageHeight);
              const scaledRadius = Math.round(
                (radius / canvasWidth) * imageWidth
              );
              const numPoints = 16;
              lastAnnotation.segmentation = [];
              for (let i = 0; i < numPoints; i++) {
                const angle = (i / numPoints) * 2 * Math.PI;
                const px = scaledX + scaledRadius * Math.cos(angle);
                const py = scaledY + scaledRadius * Math.sin(angle);
                const clampedPx = Math.round(
                  Math.max(0, Math.min(px, imageWidth))
                );
                const clampedPy = Math.round(
                  Math.max(0, Math.min(py, imageHeight))
                );
                lastAnnotation.segmentation.push(clampedPx, clampedPy);
              }
            }

            return newAnnotations;
          });
        }
      } else if (
        touches?.length === 2 &&
        touchStartDistanceRef.current !== null &&
        touchStartZoomRef.current !== null
      ) {
        const dx = touches[0].clientX - touches[1].clientX;
        const dy = touches[0].clientY - touches[1].clientY;
        const currentDistance = Math.sqrt(dx * dx + dy * dy);
        const zoomFactor = currentDistance / touchStartDistanceRef.current;
        const newZoom = Math.max(
          0.1,
          Math.min(touchStartZoomRef.current * zoomFactor, 3)
        );

        const touchX =
          (touches[0].clientX + touches[1].clientX) / 2 - rect.left;
        const touchY = (touches[0].clientY + touches[1].clientY) / 2 - rect.top;

        const mouseCanvasX = (touchX - canvas.width / 2 - panOffset.x) / zoom;
        const mouseCanvasY = (touchY - canvas.height / 2 - panOffset.y) / zoom;
        const newPanX = panOffset.x - mouseCanvasX * (newZoom - zoom);
        const newPanY = panOffset.y - mouseCanvasY * (newZoom - zoom);

        setZoom(newZoom);
        setPanOffset({ x: newPanX, y: newPanY });
      }
    },
    [
      isDrawing,
      isPanning,
      lastMousePos,
      currentTool,
      zoom,
      panOffset,
      imageLoaded,
      isEditingAnnotation,
      selectedAnnotationId,
      draggedPointIndex,
      historyIndex,
      imageDimensions,
    ]
  );

  const handleTouchEnd = useCallback(() => {
    if (longPressTimeoutRef.current) {
      clearTimeout(longPressTimeoutRef.current);
      longPressTimeoutRef.current = null;
    }

    if (isEditingAnnotation) {
      setIsEditingAnnotation(false);
      setSelectedAnnotationId(null);
      setDraggedPointIndex(null);
    } else if (isDrawing) {
      setIsDrawing(false);
      setCurrentAnnotations((prev) => {
        if (!prev?.length) return prev;
        const newAnnotations = [...prev];
        const lastAnnotation = newAnnotations[newAnnotations?.length - 1];
        if (!lastAnnotation) return newAnnotations;

        let segmentation: number[] = [];
        const canvas = canvasRef.current;
        const canvasWidth = canvas?.width || 800;
        const canvasHeight = canvas?.height || 600;
        const imageWidth = imageDimensions?.width || canvasWidth;
        const imageHeight = imageDimensions?.height || canvasHeight;

        if (lastAnnotation.type === "freehand" && lastAnnotation.points) {
          // Enhanced: Ensure polygon closure for better shape definition (same as mouse)
          if (lastAnnotation.points?.length > 2) {
            const startPoint = lastAnnotation.points[0];
            const lastPoint = lastAnnotation.points[lastAnnotation.points.length - 1];

            // Only add start point if it's not already close to the last point
            const distance = Math.sqrt(
              Math.pow(startPoint.x - lastPoint.x, 2) +
              Math.pow(startPoint.y - lastPoint.y, 2)
            );

            if (distance > 10) {
              lastAnnotation.points = [...lastAnnotation.points, startPoint];
            }
          }

          // Enhanced: Use consistent sampling method (same as mouse)
          const sampledPoints = samplePoints(lastAnnotation.points, 5 / zoom);
          segmentation = sampledPoints.reduce((acc: number[], point) => {
            const scaled = denormalizeCoordinates(
              point,
              canvasWidth,
              canvasHeight,
              imageWidth,
              imageHeight
            );
            return [...acc, scaled.x, scaled.y];
          }, []);
        } else if (
          lastAnnotation.type === "rectangle" &&
          lastAnnotation.coordinates
        ) {
          let { x, y, width = 0, height = 0 } = lastAnnotation.coordinates;
          x = Math.max(0, Math.min(x, canvasWidth - width));
          y = Math.max(0, Math.min(y, canvasHeight - height));
          const scaledX = Math.round((x / canvasWidth) * imageWidth);
          const scaledY = Math.round((y / canvasHeight) * imageHeight);
          const scaledWidth = Math.round((width / canvasWidth) * imageWidth);
          const scaledHeight = Math.round(
            (height / canvasHeight) * imageHeight
          );
          segmentation = [
            scaledX,
            scaledY,
            scaledX + scaledWidth,
            scaledY,
            scaledX + scaledWidth,
            scaledY + scaledHeight,
            scaledX,
            scaledY + scaledHeight,
            scaledX,
            scaledY,
          ];
        } else if (
          lastAnnotation.type === "circle" &&
          lastAnnotation.coordinates
        ) {
          let { x, y, radius = 0 } = lastAnnotation.coordinates;
          x = Math.max(radius, Math.min(x, canvasWidth - radius));
          y = Math.max(radius, Math.min(y, canvasHeight - radius));
          const scaledX = Math.round((x / canvasWidth) * imageWidth);
          const scaledY = Math.round((y / canvasHeight) * imageHeight);
          const scaledRadius = Math.round((radius / canvasWidth) * imageWidth);
          const numPoints = 16;
          segmentation = [];
          for (let i = 0; i < numPoints; i++) {
            const angle = (i / numPoints) * 2 * Math.PI;
            const px = scaledX + scaledRadius * Math.cos(angle);
            const py = scaledY + scaledRadius * Math.sin(angle);
            const clampedPx = Math.round(Math.max(0, Math.min(px, imageWidth)));
            const clampedPy = Math.round(
              Math.max(0, Math.min(py, imageHeight))
            );
            segmentation.push(clampedPx, clampedPy);
          }
        }
        lastAnnotation.segmentation = segmentation;
        newAnnotations[newAnnotations?.length - 1] = lastAnnotation;

        // Enhanced: Update history only when drawing is completed (same as mouse)
        setHistory((prev) => [
          ...prev.slice(0, historyIndex + 1),
          newAnnotations,
        ]);
        setHistoryIndex((prev) => prev + 1);

        // Enhanced: Save annotations after drawing completion
        setTimeout(() => {
          saveAnnotations();
        }, 50);

        return newAnnotations;
      });
    }
    if (isPanning) {
      setIsPanning(false);
      setLastMousePos(null);
    }
    touchStartDistanceRef.current = null;
    touchStartZoomRef.current = null;
  }, [isDrawing, isPanning, isEditingAnnotation, imageDimensions]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.addEventListener("wheel", handleWheel, { passive: false });
      canvas.addEventListener("touchstart", handleTouchStart as any, {
        passive: false,
      });
      canvas.addEventListener("touchmove", handleTouchMove as any, {
        passive: false,
      });
      canvas.addEventListener("touchend", handleTouchEnd as any, {
        passive: false,
      });
      canvas.addEventListener("touchcancel", handleTouchEnd as any, {
        passive: false,
      });
      return () => {
        canvas.removeEventListener("wheel", handleWheel);
        canvas.removeEventListener("touchstart", handleTouchStart as any);
        canvas.removeEventListener("touchmove", handleTouchMove as any);
        canvas.removeEventListener("touchend", handleTouchEnd as any);
        canvas.removeEventListener("touchcancel", handleTouchEnd as any);
      };
    }
  }, [handleWheel, handleTouchStart, handleTouchMove, handleTouchEnd]);

  // Enhanced: Debounced operations to prevent multiple rapid clicks
  const [isUndoRedoProcessing, setIsUndoRedoProcessing] = useState(false);
  const [isEraserProcessing, setIsEraserProcessing] = useState(false);

  const handleUndo = useCallback(() => {
    if (historyIndex > 0 && !isUndoRedoProcessing) {
      setIsUndoRedoProcessing(true);

      const newHistoryIndex = historyIndex - 1;
      const newAnnotations = history[newHistoryIndex];

      // Enhanced: Update both component state and per-image state
      setHistoryIndex(newHistoryIndex);
      setCurrentAnnotations(newAnnotations);

      // Update per-image state for current image
      const currentImageId = getImageId();
      updatePerImageState(currentImageId, {
        annotations: newAnnotations,
        history: history,
        historyIndex: newHistoryIndex
      });

      // Save the updated state to localStorage
      setTimeout(() => {
        saveAnnotations();
      }, 50);

      // Reset processing flag after a short delay
      setTimeout(() => {
        setIsUndoRedoProcessing(false);
      }, 200);

      console.log(`Undo performed for image ${currentImageId}, new index: ${newHistoryIndex}`);
    }
  }, [historyIndex, history, isUndoRedoProcessing, getImageId, updatePerImageState, saveAnnotations]);

  const handleRedo = useCallback(() => {
    if (historyIndex < history?.length - 1 && !isUndoRedoProcessing) {
      setIsUndoRedoProcessing(true);

      const newHistoryIndex = historyIndex + 1;
      const newAnnotations = history[newHistoryIndex];

      // Enhanced: Update both component state and per-image state
      setHistoryIndex(newHistoryIndex);
      setCurrentAnnotations(newAnnotations);

      // Update per-image state for current image
      const currentImageId = getImageId();
      updatePerImageState(currentImageId, {
        annotations: newAnnotations,
        history: history,
        historyIndex: newHistoryIndex
      });

      // Save the updated state to localStorage
      setTimeout(() => {
        saveAnnotations();
      }, 50);

      // Reset processing flag after a short delay
      setTimeout(() => {
        setIsUndoRedoProcessing(false);
      }, 200);

      console.log(`Redo performed for image ${currentImageId}, new index: ${newHistoryIndex}`);
    }
  }, [historyIndex, history, isUndoRedoProcessing, getImageId, updatePerImageState, saveAnnotations]);

  // Function to save annotations to Redux store
  const saveAnnotationsToRedux = useCallback(() => {
    // Always preserve existing annotations and add/update manual ones
    const currentSlotAnnotations = globalAnnotations?.[slotKey] || { numbering: [], decay: [] };

    // Prepare manual annotations if any exist
    const manualDecayAnnotations = currentAnnotations.length > 0
      ? currentAnnotations.map(annotation => ({
        category_id: annotation.category_id || 1,
        category_name: `Decay Type ${annotation.category_id || 1}`,
        segmentation: annotation.points ?
          [annotation.points.flatMap(point => [point.x, point.y])] :
          [[]],
        conf_score: 1.0,
        annotation_source: "Manual"
      }))
      : [];

    // Always update Redux store to ensure existing annotations are preserved
    const updatedAnnotations = {
      ...globalAnnotations,
      [slotKey]: {
        numbering: currentSlotAnnotations.numbering, // Preserve existing numbering
        decay: [
          // Keep existing non-manual annotations
          ...currentSlotAnnotations.decay.filter((ann: any) => ann.annotation_source !== "Manual"),
          // Add new manual annotations
          ...manualDecayAnnotations
        ]
      }
    };

    // Always dispatch to ensure state consistency
    dispatch(setAnnotations(updatedAnnotations));

    // Enhanced: Dispatch custom event to notify parent page about annotation changes
    console.log("📡 Dispatching annotationsSaved event...");
    const event = new CustomEvent('annotationsSaved', {
      detail: { slotKey, annotationsCount: manualDecayAnnotations.length }
    });
    window.dispatchEvent(event);
  }, [currentAnnotations, globalAnnotations, slotKey, dispatch]);

  // Enhanced: Session cleanup function for closing without submit
  const clearAllAnnotationData = useCallback(() => {
    // Clear all annotation storage
    annotationStorage.clearAll();

    // Clear per-image states
    setPerImageStates(new Map());

    // Reset current component state
    setCurrentAnnotations([]);
    setHistory([[]]);
    setHistoryIndex(0);
    setIsEditingAnnotation(false);
    setSelectedAnnotationId(null);
    setDraggedPointIndex(null);

    console.log("All annotation data cleared - fresh session ready");
  }, []);

  const handleSave = useCallback(() => {
    saveAnnotations();
    saveAnnotationsToRedux();
    onClose();
    onCloseCallback?.(); // Invoke the callback when closing
  }, [saveAnnotations, saveAnnotationsToRedux, onClose, onCloseCallback]);

  // Enhanced: Close handler with option to clear data (for closing without submit)
  const handleClose = useCallback((clearData: boolean = false) => {
    if (clearData) {
      // Scenario 2: Clear all data when closing without submit
      clearAllAnnotationData();
    } else {
      // Normal close: save annotations
      saveAnnotations();
      saveAnnotationsToRedux();

      // Enhanced: Dispatch event when closing normally (with save)
      console.log("📡 Dispatching annotationsSaved event on close...");
      const event = new CustomEvent('annotationsSaved', {
        detail: { action: 'close', slotKey }
      });
      window.dispatchEvent(event);
    }

    // CRITICAL FIX: Reset image state to default when closing
    // This ensures that when AI-annotator is reopened, it shows the default image
    console.log("Resetting image state to default on close");
    setCurrentMainImage(null); // Reset to null so it will use the default image prop
    setSelectedImages([]); // Clear selected images
    layoutSwitchProcessedRef.current = false; // Reset layout switch flag

    onClose();
    onCloseCallback?.(); // Invoke the callback when closing
  }, [saveAnnotations, saveAnnotationsToRedux, onClose, onCloseCallback, clearAllAnnotationData]);

  // Enhanced: Close without saving (for X button or ESC key)
  const handleCloseWithoutSave = useCallback(() => {
    handleClose(true); // Clear data when closing without submit
  }, [handleClose]);

  // Add beforeunload event listener to save annotations when page is being left
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveAnnotations();
      saveAnnotationsToRedux();
    };

    const handlePopState = () => {
      // Handle browser back button - save annotations before navigation
      console.log("Browser back button detected, saving annotations");
      saveAnnotations();
      saveAnnotationsToRedux();

      // Enhanced: Dispatch event when navigating back via browser button
      console.log("📡 Dispatching annotationsSaved event on browser back...");
      const event = new CustomEvent('annotationsSaved', {
        detail: { action: 'browserBack', slotKey }
      });
      window.dispatchEvent(event);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [saveAnnotations, saveAnnotationsToRedux]);

  // Removed auto-save useEffect to prevent infinite re-renders
  // Annotations are saved manually when needed (drawing, erasing, undo/redo)
  // Force refresh comment

  // Debug: Monitor currentAnnotations changes
  useEffect(() => {
    console.log("currentAnnotations changed:", currentAnnotations);
    console.log("currentAnnotations.length:", currentAnnotations.length);
  }, [currentAnnotations]);

  const handleSubmitFeedback = useCallback(() => {
    if (!selectedFinding || !feedbackText.trim()) return;
    runAnalysis();
    const feedback: AIFeedback = {
      id: `feedback-${Date.now()}`,
      findingId: selectedFinding.id,
      type: "missing",
      description: feedbackText,
      doctorNotes: feedbackText,
      submittedAt: new Date().toISOString(),
    };

    console.log("Submitting AI feedback:", feedback);
    setFeedbackText("");
    setSelectedFinding(null);
    saveAnnotations();
  }, [selectedFinding, feedbackText]);



  // Enhanced: Validate annotations before submission
  const validateAnnotations = useCallback((annotations: ManualAnnotation[]) => {
    return annotations.filter(annotation => {
      // Ensure annotation has valid points and segmentation
      if (annotation.type === "freehand") {
        return annotation.points &&
          annotation.points.length >= 3 &&
          annotation.segmentation &&
          annotation.segmentation.length >= 6; // At least 3 points (x,y pairs)
      }
      return true;
    });
  }, []);

  useEffect(() => {
    if (activeImageCount === 1) {
      canvasRefs.current = [canvasRef.current];
      imageRefs.current = [imageRef.current];
    }
  }, [activeImageCount]);

  const handleSubmitManualAnnotationFeedback = useCallback(async () => {
    console.log("handleSubmitManualAnnotationFeedback called");
    console.log("manualAnnotationFeedback:", manualAnnotationFeedback);
    console.log("currentAnnotations.length:", currentAnnotations.length);

    if (!manualAnnotationFeedback.trim() || currentAnnotations.length === 0) {
      console.log("Early return - missing feedback or no annotations");
      return;
    }

    // Enhanced: Validate annotations before submission
    const validAnnotations = validateAnnotations(currentAnnotations);
    if (validAnnotations.length === 0) {
      console.warn("No valid annotations to submit");
      return;
    }

    const imageObj = typeof image === 'object' ? image : null;
    const reqBody = {
      imageId: String(imageObj?.imageId || 'unknown'),
      imageType: imageObj?.type || slotInfo?.id || "unknown",
      annotationSource: "Manual",
      annotationType: "teeth_decay",
      annotation: validAnnotations.map((ann) => ({
        category_id: ann.category_id,
        segmentation: ann.segmentation,
        conf_score: 0.95,
        annotation_source: "Manual",
      })),
      updatedBy: userId,
      description: manualAnnotationFeedback,
    };

    console.log(
      "Manual Annotations Request Body",
      JSON.stringify(reqBody, null, 2)
    );

    try {
      const response = await fetchWithRefresh(
        `${UPDATE_MANUAL_ANNOTATIONS}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(reqBody),
          credentials: "include",
        },
        router
      );

      const result = await response?.json();
      console.log("API Response:", JSON.stringify(result, null, 2));
      console.log("API Response status:", response?.status);
      console.log("Is result an array?", Array.isArray(result));

      if (result && Array.isArray(result)) {
        console.log("Entering success block - processing API response");
        const newAnnotations: Annotation = {
          numbering: [],
          decay: [],
        };

        result.forEach((item: any) => {
          if (item.status !== "success" || !item.predictions) return;

          if (item.predictions.teeth_numbering) {
            item.predictions.teeth_numbering.forEach((pred: any) => {
              newAnnotations.numbering.push({
                ...pred,
                annotation_source: item.annotation_source,
                image_id: item.image_id,
              });
            });
          }

          if (item.predictions.teeth_decay) {
            item.predictions.teeth_decay.forEach((pred: any) => {
              newAnnotations.decay.push({
                ...pred,
                annotation_source: item.annotation_source,
                image_id: item.image_id,
              });
            });
          }
        });

        // Replace AI annotations instead of appending
        setStoredAnnotations((prev) => {
          const generateAnnotationKey = (ann: any) =>
            `${ann.category_id}-${ann.segmentation
              .map((c: number) => Math.round(c))
              .join("-")}`;

          // Keep only manual annotations from previous state
          const existingNumbering = (prev?.numbering || []).filter(
            (existing: any) =>
              existing.annotation_source === "Manual" &&
              !newAnnotations.numbering.some(
                (newAnn) =>
                  generateAnnotationKey(newAnn) ===
                  generateAnnotationKey(existing)
              )
          );
          const existingDecay = (prev?.decay || []).filter(
            (existing: any) =>
              existing.annotation_source === "Manual" &&
              !newAnnotations.decay.some(
                (newAnn) =>
                  generateAnnotationKey(newAnn) ===
                  generateAnnotationKey(existing)
              )
          );

          return {
            numbering: [...existingNumbering, ...newAnnotations.numbering],
            decay: [...existingDecay, ...newAnnotations.decay],
          };
        });

        console.log(
          "New Annotations in ai-anno",
          JSON.stringify(newAnnotations, null, 2)
        );

        // Update Redux store with new annotations
        dispatch(setAnnotations({
          ...globalAnnotations,     // ← merge all slots
          [slotKey]: newAnnotations,
        }));

        // Update stored annotations to reflect the new API response
        setStoredAnnotations(newAnnotations);

        // Clear manual annotations and reset state immediately
        console.log("Resetting manual annotations and UI state after API response");
        console.log("currentAnnotations before reset:", currentAnnotations);

        // Use functional updates to ensure state is properly reset
        setCurrentAnnotations(() => {
          console.log("setCurrentAnnotations called with empty array");
          return [];
        });
        setHistory(() => {
          console.log("setHistory called with empty array");
          return [[]];
        });
        setHistoryIndex(() => {
          console.log("setHistoryIndex called with 0");
          return 0;
        });
        setManualAnnotationFeedback(() => {
          console.log("setManualAnnotationFeedback called with empty string");
          return "";
        });

        console.log("State reset completed - currentAnnotations should now be empty");

        // Clear annotation storage completely to prevent restoration
        const currentImageId = getImageId();
        console.log("Clearing annotation storage for imageId:", currentImageId);

        // Use direct localStorage removal to ensure complete clearing
        localStorage.removeItem(`manual_annotations_${currentImageId}`);
        console.log("localStorage cleared for key:", `manual_annotations_${currentImageId}`);

        // Also clear any potential variations of the key
        const possibleKeys = [
          `manual_annotations_${currentImageId}`,
          `manual_annotations_${String(currentImageId)}`,
          `manual_annotations_${getImageUrl()}`,
        ];

        possibleKeys.forEach(key => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log("Cleared additional localStorage key:", key);
          }
        });

        // Clear all manual annotation keys from localStorage as a safety measure
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('manual_annotations_')) {
            const storedData = localStorage.getItem(key);
            if (storedData) {
              try {
                const parsed = JSON.parse(storedData);
                if (parsed && parsed.manual && Array.isArray(parsed.manual)) {
                  localStorage.removeItem(key);
                  console.log("Cleared manual annotation key:", key);
                }
              } catch (e) {
                // If parsing fails, remove the key anyway
                localStorage.removeItem(key);
                console.log("Cleared invalid manual annotation key:", key);
              }
            }
          }
        });

        // Set flag to prevent restoration from useEffect hooks for current image only
        setJustSubmittedFeedback(true);

        // Reset the flag after a shorter delay to minimize impact on navigation
        setTimeout(() => {
          setJustSubmittedFeedback(false);
        }, 500);

        // Force multiple state updates to ensure complete reset
        setTimeout(() => {
          console.log("Force clearing currentAnnotations again");
          setCurrentAnnotations([]);
          setHistory([[]]);
          setHistoryIndex(0);
          setManualAnnotationFeedback("");

          // Also clear localStorage again to be absolutely sure
          localStorage.removeItem(`manual_annotations_${currentImageId}`);
        }, 50);

        setTimeout(() => {
          console.log("Final check - currentAnnotations after timeout:", currentAnnotations);
          // Force one more clear to ensure state is empty
          setCurrentAnnotations([]);
        }, 200);

        // Immediate force clear using functional update to ensure it takes effect
        setCurrentAnnotations(() => {
          console.log("Functional update - forcing currentAnnotations to empty array");
          return [];
        });

        // Clear the canvas of manual drawings and redraw with fresh API annotations
        const canvas = canvasRefs.current[0];
        const img = imageRefs.current[0];
        if (canvas && img) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            // Clear the entire canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
          }
        }

        // Force multiple redraws to ensure the main image is updated with fresh annotations
        setTimeout(() => {
          if (imageLoaded) drawCanvas();
        }, 0);

        requestAnimationFrame(() => {
          if (imageLoaded) drawCanvas();
        });

        // Additional redraw after a short delay to ensure state is fully updated
        setTimeout(() => {
          if (imageLoaded) drawCanvas();
        }, 100);

        // Final redraw to ensure everything is properly rendered
        setTimeout(() => {
          if (imageLoaded) drawCanvas();
        }, 200);

        // Update slots to mark the image as analyzed
        // dispatch(
        //   setSlots({
        //     ...slots,
        //     [slotId.toUpperCase()]: {
        //       ...image,
        //       analyzed: true,
        //     },
        //   })
        // );

        // Update slots to mark the image as analyzed
        if (typeof image === 'object' && image) {
          dispatch(
            setSlots({
              ...slots,
              [slotKey]: {
                ...image,
                analyzed: true,
              },
            })
          );

          // if it was the panoramic slot, update that too
          if (slotKey === "PANORAMIC") {
            dispatch(
              setPanoramicImage({
                ...image,
                analyzed: true,
              })
            );
          }
        }

        saveAnnotations();
        if (imageLoaded) {
          drawCanvas();
        }

        // CRITICAL FIX: Preserve current image state after submission
        // Store the current image information before any potential resets
        const currentImageToPreserve = currentMainImage || image;
        const currentSlotToPreserve = slotInfo?.id || 'current';

        console.log("Preserving image after submission:", {
          currentImageToPreserve: typeof currentImageToPreserve === 'object' ? currentImageToPreserve.imageId : currentImageToPreserve,
          currentSlotToPreserve
        });

        // Ensure the current image remains displayed after submission
        if (currentImageToPreserve && typeof currentImageToPreserve === 'object') {
          // Force preservation of current image state
          setCurrentMainImage(currentImageToPreserve);
          setSelectedImages([{ slotId: currentSlotToPreserve, image: currentImageToPreserve }]);

          // Prevent the layout switch effect from overriding our selection
          layoutSwitchProcessedRef.current = true;

          // Force image to remain loaded and displayed
          setTimeout(() => {
            if (imageRef.current && imageRef.current.src !== currentImageToPreserve.url) {
              console.log("Reloading preserved image after submission");
              // Reload the current image if it was changed
              const img = new Image();
              img.crossOrigin = "anonymous";
              img.src = currentImageToPreserve.url;
              img.onload = () => {
                imageRef.current = img;
                setImageLoaded(true);
                setImageError(false);
                setImageDimensions({ width: img.width, height: img.height });
                drawCanvas();
              };
            }

            // Reset the layout switch flag after preservation is complete
            setTimeout(() => {
              layoutSwitchProcessedRef.current = false;
            }, 500);
          }, 100);
        }

        // Show success toast notification
        toast({
          title: "Success",
          description: "Manual annotations have been submitted successfully.",
          variant: "default",
        });
      } else {
        console.error("Invalid API response format:", result);
        console.log("API response was not an array, not resetting state");

        // Show error toast for invalid response
        toast({
          title: "Error",
          description: "Failed to submit manual annotations. Invalid response format.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error submitting manual annotation feedback:", error);
      console.log("API call failed, not resetting state");

      // Show error toast for submission failure
      toast({
        title: "Error",
        description: "Failed to submit manual annotations. Please try again.",
        variant: "destructive",
      });
    } finally {
      console.log("Finally block executed");
      console.log("currentAnnotations in finally block:", currentAnnotations);
    }
  }, [
    manualAnnotationFeedback,
    currentAnnotations,
    image,
    userId,
    router,
    slotKey,
    imageDimensions,
    storedAnnotations,
    dispatch,
    imageLoaded,
    globalAnnotations,
  ]);

  const handleAddTreatment = useCallback((treatment: SuggestedTreatment) => {
    console.log("Adding treatment to plan:", treatment);
  }, []);

  // Handle image selection for multi-image display



  const handleImageSelect = useCallback((slotId: string, image: XrayImage) => {
    // Prevent re-selecting the same image
    if (selectedImages.some(sel => sel.slotId === slotId)) {
      return;
    }

    console.log("User manually selected image:", { slotId, imageId: image.imageId });

    // Enhanced: Save current image state before switching
    const currentImageId = getImageId();
    if (currentImageId && currentImageId !== "unknown") {
      saveAnnotations();
    }

    // Determine if we need to switch to Button 1 layout (single image)
    const isSingleImageSelection = activeImageCount === 1 || selectedImages.length === 0;

    // Update selected images
    let newSelectedImages: Array<{ slotId: string, image: XrayImage }>;
    if (isSingleImageSelection) {
      // For Button 1 or initial selection, replace with the new image
      newSelectedImages = [{ slotId, image }];
      setActiveImageCount(1);
      setCurrentMainImage(image);

      // CRITICAL: Prevent layout switch effect from overriding this manual selection
      layoutSwitchProcessedRef.current = true;

      // Reset the flag after the selection is processed
      setTimeout(() => {
        layoutSwitchProcessedRef.current = false;
      }, 500);

      // Reset zoom and pan for Button 1 layout
      setZoom(1);
      setPanOffset({ x: 0, y: 0 });

      console.log("User manually selected image for Button 1 layout:", image.imageId);
    } else {
      // For multi-image layouts, add the new image up to the layout limit
      newSelectedImages = [...selectedImages, { slotId, image }].slice(0, activeImageCount);
    }
    setSelectedImages(newSelectedImages);

    // Enhanced: Switch to the new image's state
    const newImageId = String(image.imageId) || image.url;
    switchToImageState(newImageId);

    // Load annotations for the new image from Redux
    const slotKey = slotId.toUpperCase();
    const newAnnotations = globalAnnotations?.[slotKey] || {
      numbering: [],
      decay: [],
    };

    // Update stored annotations
    setStoredAnnotations(newAnnotations);

    // Update the per-image state with the latest stored annotations
    updatePerImageState(newImageId, {
      storedAnnotations: newAnnotations
    });

    console.log(`Switched to image: ${newImageId}, slotId: ${slotId}`);

    // Handle image loading and canvas update
    if (isSingleImageSelection) {
      // Force reset of image state
      setImageLoaded(false);
      setImageError(false);
      setImageDimensions(null);

      // Clear current image reference to force update
      imageRef.current = null;

      // Load new image
      const img = new Image();
      img.crossOrigin = "anonymous";
      img.src = image.url;

      console.log(`Loading new image: ${image.url}`);

      img.onload = () => {
        imageRef.current = img;
        setImageLoaded(true);
        setImageError(false);
        setImageDimensions({ width: img.width, height: img.height });

        // Set canvas dimensions and redraw
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext("2d");
          if (ctx) {
            // Clear canvas only when ready to draw new image
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            setButton1CanvasDimensions(canvas, img.width, img.height);

            // Force multiple redraws to ensure image updates
            requestAnimationFrame(() => {
              drawCanvas();
              // Additional redraw after a short delay to ensure everything is updated
              setTimeout(() => {
                drawCanvas();
              }, 50);
            });
          }
        }

        console.log(`Image loaded and canvas updated for: ${image.url}`);
      };

      img.onerror = () => {
        console.error("Failed to load image:", image.url);
        setImageError(true);
        setImageLoaded(false);
      };
    } else {
      // For multi-image layouts, preload images and update canvases
      const preloadImages = newSelectedImages.map((sel) => {
        return new Promise<void>((resolve, reject) => {
          const img = new Image();
          img.crossOrigin = "anonymous";
          img.src = sel.image.url;
          img.onload = () => resolve();
          img.onerror = () => {
            console.error("Failed to load image:", sel.image.url);
            reject();
          };
        });
      });

      Promise.all(preloadImages).then(() => {
        requestAnimationFrame(() => {
          drawMultipleImages();
        });
      }).catch(() => {
        setImageError(true);
      });
    }
  }, [selectedImages, activeImageCount, switchToImageState, globalAnnotations, updatePerImageState]);

  // Consolidated useEffect for handling layout switches to button 1
  useEffect(() => {
    if (activeImageCount !== 1 || !image || layoutSwitchProcessedRef.current) {
      return;
    }

    // CRITICAL FIX: Don't reset to default image if user has manually selected a different image
    // Only reset to default when:
    // 1. No current main image is set (initial load)
    // 2. Current main image is the same as the default image (no user selection)
    // 3. User just submitted feedback and we want to preserve their current image
    const hasUserSelectedDifferentImage = currentMainImage &&
      typeof currentMainImage === 'object' &&
      typeof image === 'object' &&
      currentMainImage.imageId !== (image as XrayImage).imageId;

    // If user has selected a different image and we're not in a fresh session, preserve their selection
    if (hasUserSelectedDifferentImage && selectedImages.length > 0) {
      console.log("Preserving user-selected image instead of reverting to default");
      layoutSwitchProcessedRef.current = true;
      // Reset the flag after a delay to allow future legitimate layout switches
      setTimeout(() => {
        layoutSwitchProcessedRef.current = false;
      }, 1000);
      return;
    }

    // Mark as processed to prevent infinite loop
    layoutSwitchProcessedRef.current = true;

    // Only set to default image if no user selection exists
    setCurrentMainImage(image);

    // Set selectedImages to the default image - only when appropriate
    const defaultImage = { slotId: slotInfo?.id || 'current', image: image as XrayImage };
    setSelectedImages([defaultImage]);

    // Load annotations for the default image
    const defaultSlotKey = defaultImage.slotId.toUpperCase();
    const defaultAnnotations = globalAnnotations?.[defaultSlotKey] || {
      numbering: [],
      decay: [],
    };
    setStoredAnnotations(defaultAnnotations);

    // Load manual annotations for the default image
    const defaultImageId = String((image as XrayImage).imageId) || (image as XrayImage).url;
    const savedManualAnnotations = annotationStorage.get(defaultImageId);
    if (savedManualAnnotations && savedManualAnnotations.manual && savedManualAnnotations.manual.length > 0) {
      // Only restore if there are actual annotations (not empty array from eraser)
      setCurrentAnnotations(savedManualAnnotations.manual);
      setHistory([savedManualAnnotations.manual]);
      setHistoryIndex(0);
    } else {
      // If no annotations or empty array, ensure clean state
      setCurrentAnnotations([]);
      setHistory([[]]);
      setHistoryIndex(0);
    }

    // Reset zoom and pan offset for Button 1 layout
    setZoom(1);
    setPanOffset({ x: 0, y: 0 });

    // CRITICAL FIX: Load the default image into imageRef.current for proper display
    setImageLoaded(false);
    setImageError(false);
    setImageDimensions(null);

    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = (image as XrayImage).url;

    img.onload = () => {
      imageRef.current = img; // This is the key fix - set the imageRef to the default image
      setImageLoaded(true);
      setImageError(false);
      setImageDimensions({ width: img.width, height: img.height });

      // Set canvas dimensions and redraw with the default image
      const canvas = canvasRef.current;
      if (canvas) {
        setButton1CanvasDimensions(canvas, img.width, img.height);
        requestAnimationFrame(() => {
          drawCanvas(); // Now drawCanvas will use the correct default image
        });
      }
      // Reset the processed flag after successful image load
      layoutSwitchProcessedRef.current = false;
    };

    img.onerror = () => {
      console.error("Failed to load default image:", (image as XrayImage).url);
      setImageError(true);
      setImageLoaded(false);
      layoutSwitchProcessedRef.current = false;
    };

  }, [activeImageCount, image, slotInfo?.id]);


  // Clear drawing tools when switching to multi-image layouts and reset layout switch flag
  useEffect(() => {
    if (activeImageCount > 1) {
      setCurrentTool(null);
      // Reset the layout switch flag when moving away from button 1
      layoutSwitchProcessedRef.current = false;
    }
  }, [activeImageCount]);

  // Update selected images when activeImageCount changes (multi-image layouts only)
  useEffect(() => {
    const currentImage = { slotId: slotInfo?.id || 'current', image: image as XrayImage };

    if (activeImageCount > 1) {
      // Clear any active drawing tool when switching to multi-image layout
      setCurrentTool(null);
      // Enhanced Default Image Behavior Across All Layouts
      // Ensures consistent default image display with perfect annotation alignment

      if (activeImageCount === 2) {
        if (previousActiveImageCount === 1) {
          // Button 1 to Button 2: Same default image resized for Button 2 layout
          // Maintains 100% accurate annotation alignment, provides 1 additional space
          setSelectedImages([currentImage]);
        } else {
          // Preserve existing selections or start with default
          setSelectedImages(prevSelected => {
            if (prevSelected.length === 0) return [currentImage];
            return prevSelected.slice(0, activeImageCount);
          });
        }
      } else if (activeImageCount === 4) {
        if (previousActiveImageCount === 1) {
          // Button 1 to Button 4: Same default image resized for Button 4 layout
          // Maintains 100% accurate annotation alignment, provides 3 additional spaces
          setSelectedImages([currentImage]);
        } else if (previousActiveImageCount === 2) {
          // Button 2 to Button 4: Carry forward all Button 2 images
          // All images resized for Button 4 layout with accurate annotations, provides 2 additional spaces
          setSelectedImages(prevSelected => {
            const currentSelections = prevSelected.filter(sel => sel.slotId !== currentImage.slotId);
            const newSelections = [currentImage, ...currentSelections];
            return newSelections.slice(0, activeImageCount);
          });
        } else {
          // Preserve existing selections or start with default
          setSelectedImages(prevSelected => {
            if (prevSelected.length === 0) return [currentImage];
            return prevSelected.slice(0, activeImageCount);
          });
        }
      } else if (activeImageCount === 8) {
        if (previousActiveImageCount === 1) {
          // Button 1 to Button 8: Same default image resized for Button 8 layout
          // Maintains 100% accurate annotation alignment, provides 7 additional spaces
          setSelectedImages([currentImage]);
        } else if (previousActiveImageCount === 2) {
          // Button 2 to Button 8: Carry forward all Button 2 images
          // All images resized for Button 8 layout with accurate annotations, provides 6 additional spaces
          setSelectedImages(prevSelected => {
            const currentSelections = prevSelected.filter(sel => sel.slotId !== currentImage.slotId);
            const newSelections = [currentImage, ...currentSelections];
            return newSelections.slice(0, activeImageCount);
          });
        } else if (previousActiveImageCount === 4) {
          // Button 4 to Button 8: Carry forward all Button 4 images
          // All images resized for Button 8 layout with accurate annotations, provides 4 additional spaces
          setSelectedImages(prevSelected => {
            const currentSelections = prevSelected.filter(sel => sel.slotId !== currentImage.slotId);
            const newSelections = [currentImage, ...currentSelections];
            return newSelections.slice(0, activeImageCount);
          });
        } else {
          // Preserve existing selections or start with default
          setSelectedImages(prevSelected => {
            if (prevSelected.length === 0) return [currentImage];
            return prevSelected.slice(0, activeImageCount);
          });
        }
      } else {
        // Fallback: Always ensure default image is displayed
        setSelectedImages([currentImage]);
      }
    }

    // Update previous count for next transition
    setPreviousActiveImageCount(activeImageCount);
  }, [activeImageCount, image, slotInfo, allAvailableImages, previousActiveImageCount]);

  // Enhanced: Special handling for Button 2 ↔ Button 4 transitions to prevent flickering
  useEffect(() => {
    // Fixed: Stable transition handling for Button 2 ↔ Button 4 with accurate annotations
    if ((previousActiveImageCount === 2 && activeImageCount === 4) ||
      (previousActiveImageCount === 4 && activeImageCount === 2)) {

      // Use requestAnimationFrame for smooth transition
      const animationId = requestAnimationFrame(() => {
        setTimeout(() => {
          // Fixed: Coordinated redraw to prevent annotation flickering and misalignment
          if (selectedImages.length > 0) {
            debouncedDrawMultipleImages();
          }
        }, 200); // Optimized delay for Button 2 ↔ Button 4 transitions
      });

      return () => cancelAnimationFrame(animationId);
    }
  }, [activeImageCount, previousActiveImageCount, selectedImages.length]);

  // Enhanced: Stable layout transitions with anti-flickering measures
  useEffect(() => {
    // Fixed: Prevent flickering during Button 2 ↔ Button 4 transitions
    if (activeImageCount > 1 && selectedImages.length > 0) {
      // Use requestAnimationFrame for smoother transitions
      const animationId = requestAnimationFrame(() => {
        // Batch canvas updates to prevent flickering
        const updatePromises = canvasRefs.current.map((canvas, index) => {
          return new Promise<void>((resolve) => {
            if (canvas && selectedImages[index]) {
              // Stable canvas update without custom events
              setTimeout(() => {
                resolve();
              }, 50);
            } else {
              resolve();
            }
          });
        });

        Promise.all(updatePromises).then(() => {
          // Fixed: Single coordinated redraw to prevent annotation misalignment
          if (activeImageCount === 1) {
            drawCanvas();
          } else {
            debouncedDrawMultipleImages();
          }
        });
      });

      return () => cancelAnimationFrame(animationId);
    }
  }, [activeImageCount, selectedImages]);

  // Update slider annotations when toggle buttons are clicked
  useEffect(() => {
    if (isFullScreen && allAvailableImages.length > 0) {
      // Update all slider thumbnails with current annotation visibility
      allAvailableImages.forEach(({ slotId }) => {
        const sliderImages = document.querySelectorAll(`img[alt="${slotId}"]`);
        sliderImages.forEach((imgElement) => {
          const container = imgElement.parentElement;
          if (container) {
            // Remove existing annotation canvas
            const existingCanvas = container.querySelector('.annotation-canvas');
            if (existingCanvas) {
              container.removeChild(existingCanvas);
            }

            // Fixed: Add annotation canvas only for teeth_decay (no teeth_numbering on slider)
            if (showTeethDecay && globalAnnotations) {
              const img = imgElement as HTMLImageElement;
              if (img.naturalWidth && img.naturalHeight) {
                // EXACT FullMouthSeries canvas initialization
                const initializeCanvas = () => {
                  if (!img.complete) return;

                  const annotationCanvas = document.createElement('canvas');
                  annotationCanvas.className = 'annotation-canvas';
                  annotationCanvas.style.position = 'absolute';
                  annotationCanvas.style.top = '0';
                  annotationCanvas.style.left = '0';
                  annotationCanvas.style.pointerEvents = 'none';
                  annotationCanvas.style.zIndex = '10';

                  renderSliderAnnotations(annotationCanvas, slotId, img);
                  container.appendChild(annotationCanvas);
                };

                // Initialize like FullMouthSeries
                if (img.complete) {
                  initializeCanvas();
                } else {
                  img.onload = initializeCanvas;
                }
              }
            }
          }
        });
      });
    }
  }, [showTeethDecay, isFullScreen, allAvailableImages, globalAnnotations, renderSliderAnnotations]);

  // Function to remove an image from the selection
  const handleRemoveImage = useCallback((slotIdToRemove: string) => {
    const currentImage = { slotId: slotInfo?.id || 'current', image: image as XrayImage };

    // Don't allow removing the current/default image
    if (slotIdToRemove === currentImage.slotId) {
      return;
    }

    const newSelectedImages = selectedImages.filter(sel => sel.slotId !== slotIdToRemove);
    setSelectedImages(newSelectedImages);

    // Enhanced: Smart layout transition handling with proper annotation alignment
    if (newSelectedImages.length === 1) {
      const remainingImage = newSelectedImages[0];

      // Enhanced: Always transition to Button 1 when only one image remains
      setActiveImageCount(1);
      setCurrentMainImage(remainingImage.image);

      // Reset zoom and pan offset to default values for Button 1 layout
      setZoom(1);
      setPanOffset({ x: 0, y: 0 });

      // Load annotations for the remaining image
      const remainingSlotKey = remainingImage.slotId.toUpperCase();
      const remainingAnnotations = globalAnnotations?.[remainingSlotKey] || {
        numbering: [],
        decay: [],
      };
      setStoredAnnotations(remainingAnnotations);

      // Enhanced: Load manual annotations from storage for the remaining image
      // Use the same key format as saveAnnotations() for consistency
      const remainingImageId = String(remainingImage.image.imageId) || remainingImage.image.url;
      const savedManualAnnotations = annotationStorage.get(remainingImageId);
      if (savedManualAnnotations && savedManualAnnotations.manual && savedManualAnnotations.manual.length > 0) {
        // Only restore if there are actual annotations (not empty array from eraser)
        setCurrentAnnotations(savedManualAnnotations.manual);
        setHistory([savedManualAnnotations.manual]);
        setHistoryIndex(0);
      } else {
        // If no annotations or empty array, ensure clean state
        setCurrentAnnotations([]);
        setHistory([[]]);
        setHistoryIndex(0);
      }

      // Enhanced: Force proper Button 1 display with original dimensions and accurate annotations
      setTimeout(() => {
        // Force canvas resize to original Button 1 dimensions using image aspect ratio
        const canvas = canvasRef.current;
        if (canvas && imageDimensions) {
          setButton1CanvasDimensions(canvas, imageDimensions.width, imageDimensions.height);
        } else if (canvas) {
          setButton1CanvasDimensions(canvas);
        }
        drawCanvas(); // Trigger redraw with proper Button 1 dimensions
      }, 100);

    } else if (newSelectedImages.length === 2 && activeImageCount > 2) {
      // Fixed: Button 4 to Button 2 transition - auto-adjust to Button 2 layout
      setActiveImageCount(2);

      // Trigger redraw with proper Button 2 dimensions and annotation alignment
      setTimeout(() => {
        drawCanvas();
      }, 100);

    } else if (newSelectedImages.length === 0) {
      // If no images left, add back the current image and revert to Button 1
      const currentImage = { slotId: slotInfo?.id || 'current', image: image as XrayImage };
      setSelectedImages([currentImage]);
      setActiveImageCount(1);

      // Reset zoom and pan offset to default values for Button 1 layout
      setZoom(1);
      setPanOffset({ x: 0, y: 0 });

      // Enhanced: Load manual annotations from storage for the current image
      // Use the same key format as saveAnnotations() for consistency
      const currentImageId = String((image as XrayImage).imageId) || (image as XrayImage).url;
      const savedManualAnnotations = annotationStorage.get(currentImageId);
      if (savedManualAnnotations && savedManualAnnotations.manual && savedManualAnnotations.manual.length > 0) {
        // Only restore if there are actual annotations (not empty array from eraser)
        setCurrentAnnotations(savedManualAnnotations.manual);
        setHistory([savedManualAnnotations.manual]);
        setHistoryIndex(0);
      } else {
        // If no annotations or empty array, ensure clean state
        setCurrentAnnotations([]);
        setHistory([[]]);
        setHistoryIndex(0);
      }

      // Enhanced: Proper Button 1 setup with original dimensions and accurate annotations
      setTimeout(() => {
        // Force canvas resize to original Button 1 dimensions using image aspect ratio
        const canvas = canvasRef.current;
        if (canvas && imageDimensions) {
          setButton1CanvasDimensions(canvas, imageDimensions.width, imageDimensions.height);
        } else if (canvas) {
          setButton1CanvasDimensions(canvas);
        }
        drawCanvas();
      }, 100);
    }

    // Enhanced: Smart layout adjustment for proper transitions

    // Clear canvas refs for removed images
    setTimeout(() => {
      canvasRefs.current = canvasRefs.current.slice(0, newSelectedImages.length);
      imageRefs.current = imageRefs.current.slice(0, newSelectedImages.length);
    }, 0);
  }, [selectedImages, slotInfo, image]);

  // Enhanced tool toggle with state cleanup
  const handleToolToggle = useCallback((tool: Exclude<DrawingTool, null>) => {
    // Enhanced: Clean up any ongoing drawing state when switching tools
    if (isDrawing) {
      setIsDrawing(false);
    }
    if (isEditingAnnotation) {
      setIsEditingAnnotation(false);
      setSelectedAnnotationId(null);
      setDraggedPointIndex(null);
    }
    if (isPanning) {
      setIsPanning(false);
      setLastMousePos(null);
    }

    // Clear any pending timeouts
    if (longPressTimeoutRef.current) {
      clearTimeout(longPressTimeoutRef.current);
      longPressTimeoutRef.current = null;
    }

    if (currentTool === tool) {
      // If the same tool is clicked, toggle it off
      setCurrentTool(null);
    } else {
      // Otherwise, select the new tool
      setCurrentTool(tool);
    }
  }, [currentTool, isDrawing, isEditingAnnotation, isPanning]);

  // Optimized grid layout system with maximized image sizes and minimal gaps
  const getGridLayoutClass = useCallback(() => {
    const targetCount = activeImageCount;

    if (targetCount === 1) {
      return 'grid grid-cols-1 grid-rows-1 gap-2 w-full h-full';
    } else if (targetCount === 2) {
      return 'grid grid-cols-2 grid-rows-1 gap-2 w-full h-full';
    } else if (targetCount === 4) {
      // Fixed: Optimized Button 4 layout with minimal gaps and maximized image size
      return 'grid grid-cols-2 grid-rows-2 gap-1 w-full h-full';
    } else if (targetCount === 8) {
      // Fixed: Optimized Button 8 layout with minimal gaps and increased image size
      return 'grid grid-cols-4 grid-rows-2 gap-1 w-full h-full';
    } else {
      return 'grid grid-cols-2 grid-rows-2 gap-2 w-full h-full';
    }
  }, [activeImageCount]);

  // Enhanced: Anti-overlap sizing with conservative calculations
  const getDynamicImageSize = useCallback(() => {
    const targetCount = activeImageCount;

    // Conservative main area calculation to prevent any overlap
    const mainAreaWidth = 1200; // Fixed safe width
    const mainAreaHeight = 700;  // Fixed safe height

    // Extra safety margins to ensure no overlap
    const safeAreaWidth = mainAreaWidth - 80; // 40px margin on each side
    const safeAreaHeight = mainAreaHeight - 80; // 40px margin on top and bottom

    if (targetCount === 1) {
      // Button 1: ALWAYS use max dimensions for consistency
      return {
        width: `${BUTTON_1_MAX_DIMENSIONS.width}px`,
        height: `${BUTTON_1_MAX_DIMENSIONS.height}px`,
        maxWidth: `${BUTTON_1_MAX_DIMENSIONS.width}px`,
        maxHeight: `${BUTTON_1_MAX_DIMENSIONS.height}px`,
        minHeight: `${BUTTON_1_MAX_DIMENSIONS.height}px`
      };
    } else if (targetCount === 2) {
      // Button 2: Conservative sizing to prevent overlap
      const gapSize = 8; // gap-2 = 8px
      const totalGaps = gapSize * 2; // Extra safety gap
      const availableWidth = safeAreaWidth - totalGaps;
      const width = Math.floor(availableWidth / 2) - 10; // Extra 10px safety margin
      const height = Math.min(safeAreaHeight * 0.8, 400); // Conservative height

      return {
        width: `${Math.max(width, 250)}px`, // Minimum 250px width
        height: `${Math.max(height, 200)}px`, // Minimum 200px height
        maxWidth: `${Math.max(width, 250)}px`,
        maxHeight: `${Math.max(height, 200)}px`,
        minHeight: `${Math.max(height, 200)}px`
      };
    } else if (targetCount === 4) {
      // Button 4: Conservative 2x2 grid to prevent overlap
      const gapSize = 4; // gap-1 = 4px
      const horizontalGaps = gapSize * 2; // Extra safety gaps
      const verticalGaps = gapSize * 2; // Extra safety gaps
      const availableWidth = safeAreaWidth - horizontalGaps;
      const availableHeight = safeAreaHeight - verticalGaps;
      const width = Math.floor(availableWidth / 2) - 15; // Extra 15px safety margin
      const height = Math.floor(availableHeight / 2) - 15; // Extra 15px safety margin

      return {
        width: `${Math.max(width, 200)}px`, // Minimum 200px width
        height: `${Math.max(height, 160)}px`, // Minimum 160px height
        maxWidth: `${Math.max(width, 200)}px`,
        maxHeight: `${Math.max(height, 160)}px`,
        minHeight: `${Math.max(height, 160)}px`
      };
    } else if (targetCount === 8) {
      // Button 8: Conservative 4x2 grid to prevent overlap
      const gapSize = 4; // gap-1 = 4px
      const horizontalGaps = gapSize * 4; // Extra safety gaps
      const verticalGaps = gapSize * 2; // Extra safety gaps
      const availableWidth = safeAreaWidth - horizontalGaps;
      const availableHeight = safeAreaHeight - verticalGaps;
      const width = Math.floor(availableWidth / 4) - 10; // Extra 10px safety margin
      const height = Math.floor(availableHeight / 2) - 10; // Extra 10px safety margin

      return {
        width: `${Math.max(width, 150)}px`, // Minimum 150px width
        height: `${Math.max(height, 120)}px`, // Minimum 120px height
        maxWidth: `${Math.max(width, 150)}px`,
        maxHeight: `${Math.max(height, 120)}px`,
        minHeight: `${Math.max(height, 120)}px`
      };
    } else {
      return {
        width: '300px',
        height: '240px',
        maxWidth: '300px',
        maxHeight: '240px',
        minHeight: '240px'
      };
    }
  }, [activeImageCount]);

  if (!isOpen) return null;

  const containerClass = isFullScreen
    ? "min-h-screen bg-gray-50"
    : "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4";

  const cardClass = isFullScreen
    ? "w-full h-full flex flex-col"
    : "w-full h-full max-w-7xl max-h-[95vh] flex flex-col";


  console.log("currentAnnotations", currentAnnotations);

  return (
    <div className={containerClass}>
      <style jsx>{`
        @keyframes blink {
          0%, 50% { border-color: currentColor; }
          51%, 100% { border-color: transparent; }
        }
        @keyframes blinkBlue {
          0%, 50% { border-color: #3b82f6; box-shadow: 0 0 8px rgba(59, 130, 246, 0.6); }
          51%, 100% { border-color: transparent; box-shadow: none; }
        }
      `}</style>
      <Card className={cardClass}>
        <CardHeader className="flex-shrink-0 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {isFullScreen && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  className="flex items-center gap-2"
                  title="Go back to Full Mouth Series (saves annotations)"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
              )}
              <div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  {patientName}
                </CardTitle>
                {/* {slotInfo && (
                  <p className="text-sm text-gray-600 mt-1">
                    {slotInfo.name} - {FMS_SLOT_LABELS[slotInfo.id]}
                  </p>
                )} */}
              </div>
            </div>
            <div>
              {/* Drawing tools - disabled in multi-image layouts */}
              {(() => {
                const isMultiImageLayout = activeImageCount > 1;
                const toolsDisabledMessage = isMultiImageLayout ? "Drawing tools are disabled in multi-image view. Switch to Button 1 layout to use drawing tools." : "";

                return (
                  <div className="flex flex-row gap-x-4 gap-y-3">
                    <Button
                      variant={currentTool === "pen" ? "default" : "outline"}
                      size="sm"
                      disabled={isMultiImageLayout}
                      onClick={() => handleToolToggle("pen")}
                      title={isMultiImageLayout ? toolsDisabledMessage : (currentTool === "pen" ? "Disable pen tool" : "Enable pen tool")}
                    >
                      <Pen className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={currentTool === "eraser" ? "default" : "outline"}
                      size="sm"
                      disabled={isMultiImageLayout || isEraserProcessing}
                      onClick={() => handleToolToggle("eraser")}
                      title={isMultiImageLayout ? toolsDisabledMessage : (currentTool === "eraser" ? "Disable eraser tool" : "Enable eraser tool")}
                    >
                      <Eraser className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={currentTool === "move" ? "default" : "outline"}
                      size="sm"
                      disabled={isMultiImageLayout}
                      onClick={() => handleToolToggle("move")}
                      title={isMultiImageLayout ? toolsDisabledMessage : (currentTool === "move" ? "Disable move tool" : "Enable move tool")}
                    >
                      <Move className="h-4 w-4" />
                    </Button>
                    <div className="flex flex-row flex-wrap gap-x-4 gap-y-3">
                      <div className="flex gap-4">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isMultiImageLayout}
                          onClick={() => setZoom((prev) => Math.min(prev + 0.1, 3))}
                          title={isMultiImageLayout ? toolsDisabledMessage : "Zoom in (increase image size)"}
                        >
                          <ZoomIn className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isMultiImageLayout}
                          onClick={() =>
                            setZoom((prev) => Math.max(prev - 0.1, 0.1))
                          }
                          title={isMultiImageLayout ? toolsDisabledMessage : "Zoom out (decrease image size)"}
                        >
                          <ZoomOut className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex gap-4">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isMultiImageLayout}
                          onClick={() => setRotation((prev) => prev - 90)}
                          title={isMultiImageLayout ? toolsDisabledMessage : "Rotate image left (counterclockwise)"}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isMultiImageLayout}
                          onClick={() => setRotation((prev) => prev + 90)}
                          title={isMultiImageLayout ? toolsDisabledMessage : "Rotate image right (clockwise)"}
                        >
                          <RotateCw className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex gap-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleUndo}
                          disabled={isMultiImageLayout || historyIndex === 0 || isUndoRedoProcessing}
                          title={isMultiImageLayout ? toolsDisabledMessage : (historyIndex === 0 ? "No actions to undo" : "Undo last action")}
                        >
                          <Undo className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleRedo}
                          disabled={isMultiImageLayout || historyIndex === history.length - 1 || isUndoRedoProcessing}
                          title={isMultiImageLayout ? toolsDisabledMessage : (historyIndex === history.length - 1 ? "No actions to redo" : "Redo last undone action")}
                        >
                          <Redo className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Enhanced multi-image display buttons with Button 1 restriction */}
            {isFullScreen && (
              <div className="flex gap-2 ml-4">
                <span className="text-sm text-gray-600 self-center">Images:</span>
                {[1, 2, 4].map((count) => {
                  // Enhanced: Restrict buttons based on current active images count
                  const currentActiveImages = selectedImages.length;
                  const isLayoutTooSmall = currentActiveImages > count;
                  const isDisabled = isLayoutTooSmall;

                  return (
                    <Button
                      key={count}
                      variant={activeImageCount === count ? "default" : "outline"}
                      size="sm"
                      disabled={isDisabled}
                      onClick={() => {
                        if (!isDisabled) {
                          // Enhanced: Check if layout can accommodate current active images
                          const currentActiveImages = selectedImages.length;
                          const targetLayout = count as 1 | 2 | 4 | 8;

                          // Restriction: Prevent switching to smaller layouts if too many active images
                          if (currentActiveImages > targetLayout) {
                            // Show user feedback that they need to remove images first
                            console.warn(`Cannot switch to ${targetLayout}-image layout. Currently have ${currentActiveImages} active images. Please remove ${currentActiveImages - targetLayout} image(s) first.`);
                            return; // Prevent layout switch
                          }

                          // Enhanced: Reset annotation states during layout transitions
                          setShowTeethNumbering(false);
                          setShowTeethDecay(true);
                          setActiveImageCount(targetLayout);

                          // Reset zoom and pan offset to default values when switching to Button 1 layout
                          if (targetLayout === 1) {
                            setZoom(1);
                            setPanOffset({ x: 0, y: 0 });

                            // Enhanced: Load manual annotations when switching to Button 1 layout
                            // Use the same key format as saveAnnotations() for consistency
                            const currentImageId = String((image as XrayImage).imageId) || (image as XrayImage).url;
                            const savedManualAnnotations = annotationStorage.get(currentImageId);
                            if (savedManualAnnotations && savedManualAnnotations.manual && savedManualAnnotations.manual.length > 0) {
                              // Only restore if there are actual annotations (not empty array from eraser)
                              console.log(`Layout switch: Restoring ${savedManualAnnotations.manual.length} annotations for image ${currentImageId}`);
                              setCurrentAnnotations(savedManualAnnotations.manual);
                              setHistory([savedManualAnnotations.manual]);
                              setHistoryIndex(0);
                            } else {
                              // If no annotations or empty array, ensure clean state
                              console.log(`Layout switch: No annotations to restore for image ${currentImageId} (erased or never drawn)`);
                              setCurrentAnnotations([]);
                              setHistory([[]]);
                              setHistoryIndex(0);
                            }
                          }

                          // Enhanced: Force immediate rendering for multi-image layouts
                          if (targetLayout > 1 && selectedImages.length > 0) {
                            setTimeout(() => {
                              drawMultipleImages();
                            }, 10); // Immediate rendering
                          }
                        }
                      }}
                      title={`Switch to ${count}-image layout`}
                      className=""
                    >
                      {count}
                    </Button>
                  );
                })}
              </div>
            )}
            {!isFullScreen && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    handleCloseWithoutSave();
                  }}
                  title="Close without saving annotations (clears all annotation data)"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col gap-4 overflow-hidden">
          <div className="flex gap-4 flex-1 overflow-hidden">
            {/* Left Sidebar: Tools and AI Findings */}
            <div className="w-80 flex-shrink-0 flex flex-col overflow-auto" style={{ height: "80vh", maxHeight: "800px" }}>
              <Tabs defaultValue="tools" className="w-full flex flex-col" style={{ height: "100%" }}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="tools">Tools</TabsTrigger>
                  <TabsTrigger value="ai">AI Findings</TabsTrigger>
                </TabsList>

                <TabsContent value="tools" className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2 text-gray-900">
                      Decay Type
                    </h3>
                    <Select
                      value={selectedDecayType}
                      onValueChange={setSelectedDecayType}
                      defaultValue="1"
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select decay type" />
                      </SelectTrigger>
                      <SelectContent className="w-full">
                        {decayDropdownOptions.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value}
                            className="w-full"
                          >
                            <div className="flex items-center justify-between w-full px-2 py-1 gap-10">
                              <span
                                className="w-3 h-3 rounded-full flex-shrink-0"
                                style={{
                                  backgroundColor: getAnnotationColor(
                                    option.value
                                  ),
                                }}
                              ></span>
                              <span className="text-sm flex-shrink-0">
                                {option.label}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <div className="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Color Legend</h4>
                      <ul className="space-y-2">
                        <li className="flex items-start gap-2">
                          <span
                            className="w-4 h-4 rounded-full mt-0.5 flex-shrink-0"
                            style={{ backgroundColor: getAnnotationColor("1") }}
                          />
                          <div>
                            <p className="text-sm font-medium text-gray-800">Incipient Decay</p>
                            <p className="text-xs text-gray-500">Early stage - demineralization</p>
                          </div>
                        </li>
                        <li className="flex items-start gap-2">
                          <span
                            className="w-4 h-4 rounded-full mt-0.5 flex-shrink-0"
                            style={{ backgroundColor: getAnnotationColor("2") }}
                          />
                          <div>
                            <p className="text-sm font-medium text-gray-800">Moderate Decay</p>
                            <p className="text-xs text-gray-500">Enamel affected</p>
                          </div>
                        </li>
                        <li className="flex items-start gap-2">
                          <span
                            className="w-4 h-4 rounded-full mt-0.5 flex-shrink-0"
                            style={{ backgroundColor: getAnnotationColor("3") }}
                          />
                          <div>
                            <p className="text-sm font-medium text-gray-800">Advanced Decay</p>
                            <p className="text-xs text-gray-500">Dentin affected</p>
                          </div>
                        </li>
                        <li className="flex items-start gap-2">
                          <span
                            className="w-4 h-4 rounded-full mt-0.5 flex-shrink-0"
                            style={{ backgroundColor: getAnnotationColor("4") }}
                          />
                          <div>
                            <p className="text-sm font-medium text-gray-800">Severe Decay</p>
                            <p className="text-xs text-gray-500">Pulp involvement</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <h3 className="font-semibold mb-2 text-gray-900">
                      View Annotation
                    </h3>
                    <div className="space-y-2">
                      <Button
                        variant={showTeethNumbering ? "default" : "outline"}
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => setShowTeethNumbering((prev) => !prev)}
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: "#00FF00" }}
                        />
                        Teeth Numbering{" "}
                        {showTeethNumbering ? (
                          <Eye className="h-4 w-4 ml-auto" />
                        ) : (
                          <EyeOff className="h-4 w-4 ml-auto" />
                        )}
                      </Button>
                      <Button
                        variant={showTeethDecay ? "default" : "outline"}
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => setShowTeethDecay((prev) => !prev)}
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: "red" }}
                        />
                        Teeth Decay{" "}
                        {showTeethDecay ? (
                          <Eye className="h-4 w-4 ml-auto" />
                        ) : (
                          <EyeOff className="h-4 w-4 ml-auto" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <div className="space-y-2">
                      <div className="space-y-2">
                        <h3 className="font-semibold mb-4 mt-3">
                          Image Adjustments
                        </h3>
                        <div className="flex items-center gap-2">
                          <label
                            htmlFor="brightness-range"
                            className="text-sm w-20"
                          >
                            Brightness:
                          </label>
                          <input
                            id="brightness-range"
                            type="range"
                            min="0"
                            max="200"
                            value={brightness}
                            onChange={(e) =>
                              setBrightness(Number(e.target.value))
                            }
                            className="flex-1"
                          />
                          <span className="w-8 text-right text-xs">
                            {brightness}%
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <label
                            htmlFor="contrast-range"
                            className="text-sm w-20"
                          >
                            Contrast:
                          </label>
                          <input
                            id="contrast-range"
                            type="range"
                            min="0"
                            max="200"
                            value={contrast}
                            onChange={(e) => setContrast(Number(e.target.value))}
                            className="flex-1"
                          />
                          <span className="w-8 text-right text-xs">
                            {contrast}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {currentAnnotations.length > 0 && (
                    <>
                      <Separator />
                      <div className="space-y-3">
                        <h4 className="font-medium text-gray-900 flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-orange-500" />
                          Submit AI Missing ({currentAnnotations.length}{" "}
                          {currentAnnotations.length === 1 ? "annotation" : "annotations"})
                        </h4>
                        {/* Enhanced: Show annotation details */}
                        <div className="text-xs text-gray-600">
                          {currentAnnotations.map((ann, index) => (
                            <div key={ann.id} className="flex justify-between">
                              <span>Annotation {index + 1}:</span>
                              <span>{ann.points?.length || 0} points</span>
                            </div>
                          ))}
                        </div>
                        <Textarea
                          placeholder="Describe what the AI missed that you've manually annotated..."
                          value={manualAnnotationFeedback}
                          onChange={(e) =>
                            setManualAnnotationFeedback(e.target.value)
                          }
                          className="min-h-[80px]"
                        />
                        <Button
                          size="sm"
                          onClick={handleSubmitManualAnnotationFeedback}
                          disabled={!manualAnnotationFeedback.trim()}
                          className="w-full"
                        >
                          <Send className="h-4 w-4 mr-2" />
                          Submit Missing Findings
                        </Button>
                      </div>
                    </>
                  )}
                </TabsContent>

                <TabsContent value="ai" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">
                      AI Findings ({aiFindings.length})
                    </h3>
                    {/* <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAIFindings((prev) => !prev)}
                  >
                    {showAIFindings ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button> */}
                  </div>

                  <ScrollArea className="h-84">
                    <div className="space-y-3">
                      {aiFindings.length === 0 ? (
                        <p className="text-sm text-gray-500">
                          No AI findings available.
                        </p>
                      ) : (
                        aiFindings.map((finding) => (
                          <Card
                            key={finding.id}
                            className={`p-3 cursor-pointer hover:bg-gray-50 ${highlightedFinding === finding.id
                              ? "ring-2 ring-yellow-400 bg-yellow-50"
                              : ""
                              }`}
                            onClick={() => setSelectedFinding(finding)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <Badge variant="outline" className="text-xs">
                                    {finding.procedureCode}
                                  </Badge>
                                  <span className="text-sm font-medium">
                                    Tooth {finding.toothNumber}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600">
                                  {finding.description}
                                </p>
                                <p className="text-xs text-gray-500">
                                  Confidence:{" "}
                                  {(finding.confidence * 100).toFixed(0)}% |
                                  Severity: {finding.severity}
                                </p>
                                {finding.suggestedTreatment && (
                                  <div className="mt-2">
                                    <p className="text-sm font-medium">
                                      Suggested:{" "}
                                      {finding.suggestedTreatment.procedure}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {finding.suggestedTreatment.description} |
                                      Urgency:{" "}
                                      {finding.suggestedTreatment.urgency}
                                    </p>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="mt-2"
                                      onClick={() =>
                                        handleAddTreatment(
                                          finding.suggestedTreatment
                                        )
                                      }
                                    >
                                      <Plus className="h-4 w-4 mr-2" />
                                      Add to Treatment Plan
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </Card>
                        ))
                      )}
                    </div>
                  </ScrollArea>

                  {selectedFinding && (
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                        Provide Feedback
                      </h4>
                      <Textarea
                        placeholder="Provide feedback on this AI finding..."
                        value={feedbackText}
                        onChange={(e) => setFeedbackText(e.target.value)}
                        className="min-h-[80px]"
                      />
                      <Button
                        size="sm"
                        onClick={handleSubmitFeedback}
                        disabled={!feedbackText.trim()}
                        className="w-full"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Submit Feedback
                      </Button>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>

            {/* Main Canvas Area */}
            <div
              className="flex-1 flex flex-col items-center justify-center relative"
              ref={containerRef}
            >
              {imageError ? (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <AlertTriangle className="h-8 w-8 mb-2" />
                  <p>Failed to load image.</p>
                </div>
              ) : !imageLoaded ? (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <p>Loading image...</p>
                </div>
              ) : (
                activeImageCount === 1 ? (
                  // Enhanced: Button 1 - Single image mode with original dimensions
                  (() => {
                    const currentSlotId = selectedImages.length > 0 ? selectedImages[0].slotId : slotInfo?.id || 'current';
                    const currentImageHasDecay = hasDecay(currentSlotId);
                    const borderColorClass = currentImageHasDecay ? 'border-red-500' : 'border-green-500';

                    return (
                      <canvas
                        key={currentMainImage ? (typeof currentMainImage === 'object' ? currentMainImage.imageId || currentMainImage.url : currentMainImage) : 'default'}
                        ref={canvasRef}
                        className={`border-2 ${borderColorClass}`}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        style={{
                          touchAction: "none",
                          width: "90%",
                          height: "80vh",
                          maxWidth: "1200px",
                          maxHeight: "800px"
                        }}
                      />
                    );
                  })()
                ) : (
                  // Enhanced multi-image mode with consistent default image behavior and placeholder spaces
                  <div className={getGridLayoutClass()}>
                    {/* Render selected images with dynamic sizing for perfect annotation alignment */}
                    {selectedImages.map((selectedImg, index) => {
                      const isCurrentImage = slotInfo?.id === selectedImg.slotId;
                      const imageHasDecay = hasDecay(selectedImg.slotId);
                      const borderColorClass = imageHasDecay ? 'border-red-500' : 'border-green-500';
                      const dynamicSize = getDynamicImageSize();

                      return (
                        <div
                          key={`${selectedImg.slotId}-${index}`}
                          className={`relative border-2 ${borderColorClass} min-h-0 group flex items-center justify-center rounded-lg overflow-hidden`}
                          style={{
                            width: dynamicSize.width,
                            height: dynamicSize.height,
                            minHeight: dynamicSize.minHeight,
                            maxHeight: dynamicSize.maxHeight,
                          }}
                        >
                          <div
                            className="relative w-full h-full flex items-center justify-center"
                            style={{
                              width: dynamicSize.width,
                              height: dynamicSize.height,
                              maxWidth: dynamicSize.maxWidth,
                              maxHeight: dynamicSize.maxHeight,
                            }}
                          >
                            <canvas
                              key={`multi-${selectedImg.slotId}-${selectedImg.image.imageId || selectedImg.image.url}`}
                              ref={(el) => {
                                if (index === 0) canvasRef.current = el;
                                canvasRefs.current[index] = el;
                              }}
                              className="border-0"
                              onMouseDown={handleMouseDown}
                              onMouseMove={handleMouseMove}
                              onMouseUp={handleMouseUp}
                              style={{
                                touchAction: "none",
                                width: dynamicSize.width,
                                height: dynamicSize.height,
                                maxWidth: dynamicSize.maxWidth,
                                maxHeight: dynamicSize.maxHeight,
                                objectFit: "contain",
                                display: "block"
                              }}
                              data-image-index={index}
                              data-slot-id={selectedImg.slotId}
                            />
                          </div>
                          <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded z-20">
                            {FMS_SLOT_LABELS[selectedImg.slotId]}
                          </div>
                          {/* Close button - only show for non-current images and on hover */}
                          {!isCurrentImage && (
                            <button
                              onClick={() => handleRemoveImage(selectedImg.slotId)}
                              className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20"
                              title="Remove image"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                      );
                    })}

                    {/* Enhanced: Render placeholder spaces for additional image selection (Button 2, 4, 8) */}
                    {activeImageCount > 1 && Array.from({ length: activeImageCount - selectedImages.length }, (_, index) => {
                      const placeholderIndex = selectedImages.length + index;
                      const dynamicSize = getDynamicImageSize();

                      return (
                        <div
                          key={`placeholder-${placeholderIndex}`}
                          className="relative border-2 border-dashed border-gray-300 hover:border-blue-400 min-h-0 group flex items-center justify-center rounded-lg bg-gray-50 hover:bg-blue-50 transition-all duration-200 cursor-pointer"
                          style={{
                            width: dynamicSize.width,
                            height: dynamicSize.height,
                            minHeight: dynamicSize.minHeight,
                            maxHeight: dynamicSize.maxHeight,
                          }}
                          onClick={() => {
                            // Scroll to slider for image selection
                            const slider = document.querySelector('.overflow-x-auto');
                            if (slider) {
                              slider.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                            }
                          }}
                        >
                          <div className="flex flex-col items-center justify-center text-gray-400 group-hover:text-blue-500 transition-colors duration-200">
                            <div className="w-8 h-8 border-2 border-current rounded-full flex items-center justify-center mb-2">
                              <span className="text-lg font-bold">+</span>
                            </div>
                            <span className="text-xs font-medium">
                              {activeImageCount === 2 ? 'Select Image' :
                                activeImageCount === 4 ? `Select Images` :
                                  activeImageCount === 8 ? `Select Images` : 'Select Image'}
                            </span>
                            <span className="text-xs text-gray-300 group-hover:text-blue-400">from slider below</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )
              )}
            </div>
          </div>

          {/* Image Slider - only show in full-screen mode */}
          {isFullScreen && allAvailableImages.length > 0 && (
            <div className="border-t bg-gray-50 p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-3">All Images</h3>
              <div className="flex gap-3 overflow-x-auto pb-2">
                {allAvailableImages.map(({ slotId, image }) => {
                  const isSelected = selectedImages.some(sel => sel.slotId === slotId);
                  const imageHasDecay = hasDecay(slotId);

                  // Enhanced border styling with blue animation for selected images
                  let borderColor = 'border-gray-300 hover:border-gray-400';
                  let animationStyle = 'none';

                  if (isSelected) {
                    borderColor = 'border-blue-500';
                    animationStyle = 'blinkBlue 1.2s infinite';
                  } else if (imageHasDecay) {
                    borderColor = 'border-red-500';
                  } else {
                    borderColor = 'border-green-500';
                  }

                  return (
                    <div
                      key={slotId}
                      className={`flex-shrink-0 cursor-pointer transition-all`}
                      onClick={() => handleImageSelect(slotId, image)}
                    >
                      <div
                        className={`w-32 h-32 rounded-lg border-4 overflow-hidden ${borderColor} transition-all duration-200 relative`}
                        style={{
                          animation: "",
                          position: "relative",
                          backgroundColor: "#f8f9fa"
                        }}
                      >
                        <img
                          src={image.url}
                          alt={slotId}
                          className="absolute inset-0 w-full h-full"
                          style={{
                            objectFit: "contain",
                            objectPosition: "center"
                          }}
                          onLoad={(e) => {
                            // Render annotations on slider thumbnail when image loads
                            if ((showTeethNumbering || showTeethDecay) && globalAnnotations) {
                              const imgElement = e.target as HTMLImageElement;
                              const container = imgElement.parentElement;

                              if (container) {
                                // Remove any existing annotation canvas
                                const existingCanvas = container.querySelector('.annotation-canvas');
                                if (existingCanvas) {
                                  container.removeChild(existingCanvas);
                                }

                                // Enhanced: Precise canvas initialization for perfect alignment
                                const initializeSliderCanvas = () => {
                                  if (!imgElement.complete || imgElement.naturalWidth === 0) {
                                    return;
                                  }

                                  // Create canvas overlay with precise positioning
                                  const annotationCanvas = document.createElement('canvas');
                                  annotationCanvas.className = 'annotation-canvas';
                                  annotationCanvas.style.position = 'absolute';
                                  annotationCanvas.style.pointerEvents = 'none';
                                  annotationCanvas.style.zIndex = '10';
                                  annotationCanvas.style.borderRadius = '0.5rem'; // Match container border radius

                                  // Render annotations using EXACT FullMouthSeries approach
                                  renderSliderAnnotations(
                                    annotationCanvas,
                                    slotId,
                                    imgElement
                                  );

                                  // Add canvas to container
                                  container.appendChild(annotationCanvas);
                                };

                                // Initialize immediately if image is ready, otherwise wait
                                if (imgElement.complete) {
                                  initializeSliderCanvas();
                                } else {
                                  imgElement.onload = initializeSliderCanvas;
                                }
                              }
                            }
                          }}
                        />
                      </div>
                      <div className="text-xs text-center mt-1 text-gray-600 truncate w-32">
                        {FMS_SLOT_LABELS[slotId]}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
