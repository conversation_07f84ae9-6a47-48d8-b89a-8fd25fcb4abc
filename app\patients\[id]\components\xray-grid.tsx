"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Upload, <PERSON>rk<PERSON>, Loader2 } from "lucide-react"
import type { XrayPosition } from "../types"

interface XrayGridProps {
  uploadedImages: string[]
  xrayPositions: XrayPosition[]
  selectedImageForAnalysis: number | null
  isAnalyzing: boolean
  analysisComplete: boolean
  showAIAnnotations: boolean
  mockAIAnnotations: { [key: number]: any[] }
  onImageClick: (index: number) => void
  onImageUpload: (index: number) => void
  onRunSingleImageAnalysis: (index: number) => void
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void
  onDrop: (e: React.DragEvent<HTMLDivElement>, index: number) => void
}

export default function XrayGrid({
  uploadedImages,
  xrayPositions,
  selectedImageForAnalysis,
  isAnalyzing,
  analysisComplete,
  showAIAnnotations,
  mockAIAnnotations,
  onImageClick,
  onImageUpload,
  onRunSingleImageAnalysis,
  onDragOver,
  onDrop,
}: XrayGridProps) {
  const renderXraySlot = (position: XrayPosition, index: number) => (
    <div
      key={position.id}
      className="bg-jedai-navy rounded-lg p-2 aspect-square flex flex-col items-center justify-center cursor-pointer hover:bg-opacity-80 transition-colors relative"
      onClick={() => onImageClick(index)}
      onDragOver={onDragOver}
      onDrop={(e) => onDrop(e, index)}
    >
      {uploadedImages[index] ? (
        <>
          <img
            src={uploadedImages[index] || "/placeholder.svg"}
            alt={position.label}
            className="w-full h-full object-cover rounded"
          />
          {selectedImageForAnalysis === index && (
            <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
              <Loader2 className="h-8 w-8 text-jedai-white animate-spin" />
            </div>
          )}
          {uploadedImages[index] && !isAnalyzing && !selectedImageForAnalysis && (
            <div className="absolute top-1 right-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 bg-jedai-navy/80 text-jedai-white hover:bg-jedai-gold hover:text-jedai-navy"
                onClick={(e) => {
                  e.stopPropagation()
                  onRunSingleImageAnalysis(index)
                }}
              >
                <Sparkles className="h-3 w-3" />
              </Button>
            </div>
          )}
          {analysisComplete && showAIAnnotations && mockAIAnnotations[index] && (
            <div className="absolute bottom-1 left-1 bg-jedai-gold text-jedai-navy text-xs px-1 rounded font-bold">
              AI
            </div>
          )}
        </>
      ) : (
        <Button
          variant="ghost"
          className="h-full w-full flex flex-col gap-1 text-jedai-gray hover:text-jedai-white"
          onClick={(e) => {
            e.stopPropagation()
            onImageUpload(index)
          }}
        >
          <Upload className="h-5 w-5" />
          <span className="text-xs">Upload</span>
        </Button>
      )}
      <div className="text-center mt-2 text-xs text-jedai-gray">
        <div>{position.type}</div>
        <div>{position.position}</div>
        <div>{position.number}</div>
      </div>
    </div>
  )

  return (
    <div className="grid grid-cols-7 gap-2">
      {/* Top row - Periapical Upper */}
      {xrayPositions.slice(0, 7).map((position, index) => renderXraySlot(position, index))}

      {/* Middle row - Bitewings */}
      {xrayPositions.slice(7, 11).map((position, index) => renderXraySlot(position, index + 7))}

      {/* Empty cells in the middle */}
      <div className="bg-transparent"></div>
      <div className="bg-transparent"></div>
      <div className="bg-transparent"></div>

      {/* Bottom row - Periapical Lower */}
      {xrayPositions.slice(11).map((position, index) => renderXraySlot(position, index + 11))}
    </div>
  )
}
