// components/DecayOverlayImage.tsx
"use client";

import { useRef, useEffect } from "react";
import { Prediction } from "../types";

interface Props {
  url: string;
  decay: Prediction[];
  className?: string;
}

export function DecayOverlayImage({ url, decay, className }: Props) {
  const imgRef    = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // normalize from original image coords → displayed canvas coords
  const normalize = (
    [x, y]: [number, number],
    cw: number, ch: number,
    iw: number, ih: number
  ) => ({
    x: (x / iw) * cw,
    y: (y / ih) * ch,
  });

  const draw = () => {
    const img = imgRef.current;
    const cv  = canvasRef.current;
    if (!img || !cv) return;

    const ctx = cv.getContext("2d");
    if (!ctx) return;

    // match canvas size to rendered img
    const w = img.clientWidth;
    const h = img.clientHeight;
    cv.width  = w;
    cv.height = h;
    ctx.clearRect(0, 0, w, h);

    const iw = img.naturalWidth;
    const ih = img.naturalHeight;

    // only draw if we actually have polygons
    if (decay.length) {
      decay.forEach(finding => {
        finding.segmentation.forEach(points => {
          if (points.length < 4) return;
          ctx.beginPath();
          for (let i = 0; i < points.length; i += 2) {
            const { x, y } = normalize(
              [points[i], points[i + 1]],
              w, h, iw, ih
            );
            i === 0 ? ctx.moveTo(x, y) : ctx.lineTo(x, y);
          }
          ctx.closePath();
          ctx.fillStyle   = "rgba(255,0,0,0.4)";
          ctx.strokeStyle = "rgba(255,0,0,0.8)";
          ctx.lineWidth   = 2;
          ctx.fill();
          ctx.stroke();
        });
      });
    }
  };

  // redraw whenever URL or mask data changes, after the img has loaded
  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    const handleLoad = () => draw();
    img.addEventListener("load", handleLoad);
    if (img.complete) draw();

    return () => {
      img.removeEventListener("load", handleLoad);
    };
  }, [url, decay]);

  return (
    <div className={`relative ${className ?? ""}`}>
      <img
        ref={imgRef}
        src={url}
        alt="X-ray"
        className="w-full h-full object-cover rounded"
      />
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
      />
    </div>
  );
}
