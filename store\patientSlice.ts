import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';

interface PatientState {
  id: string | null;
}

const initialState: PatientState = {
  id: Cookies.get('patientId') || null,
};

const patientSlice = createSlice({
  name: 'patient',
  initialState,
  reducers: {
    setPatientId(state, action: PayloadAction<string>) {
      state.id = action.payload;
      Cookies.set('patientId', action.payload);
    },
  },
});

export const { setPatientId } = patientSlice.actions;
// export const selectPatientId = (state: any) => state.patient.id;
export default patientSlice.reducer;