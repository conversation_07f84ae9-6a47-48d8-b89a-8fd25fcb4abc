import type { TreatmentView, TreatmentPhase } from "./types"

export const getViewHeading = (treatmentView: TreatmentView): string => {
  switch (treatmentView) {
    case "severity":
      return "Treatment Plan by Severity"
    case "quadrant":
      return "Treatment Plan by Quadrants"
    case "halfMouth":
      return "Treatment Plan by Half Mouth"
    case "fullMouth":
      return "Treatment Plan by Treatment Phases"
    default:
      return "Treatment Plan"
  }
}

export const getFilteredProcedures = (treatmentView: TreatmentView, treatmentPhases: TreatmentPhase[]) => {
  switch (treatmentView) {
    case "severity":
      return {
        Severe: treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.severity === "severe")),
        Moderate: treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.severity === "moderate")),
        Mild: treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.severity === "mild")),
      }
    case "quadrant":
      return {
        "Upper Right": treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.quadrant === "Upper Right")),
        "Upper Left": treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.quadrant === "Upper Left")),
        "Lower Right": treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.quadrant === "Lower Right")),
        "Lower Left": treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.quadrant === "Lower Left")),
      }
    case "halfMouth":
      return {
        Upper: treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.halfMouth === "upper")),
        Lower: treatmentPhases.flatMap((phase) => phase.procedures.filter((p) => p.halfMouth === "lower")),
      }
    case "fullMouth":
      return {
        "Phase 1: Urgent Care": treatmentPhases[0].procedures,
        "Phase 2: Restorative Care": treatmentPhases[1].procedures,
        "Phase 3: Preventative Care": treatmentPhases[2].procedures,
      }
    default:
      return {
        All: treatmentPhases.flatMap((phase) => phase.procedures),
      }
  }
}

export const getUrgencyColor = (urgency: string): string => {
  switch (urgency) {
    case "immediate":
      return "bg-red-100 text-red-700 border-red-300"
    case "short-term":
      return "bg-amber-100 text-amber-700 border-amber-300"
    case "preventative":
      return "bg-blue-100 text-blue-700 border-blue-300"
    case "optional":
      return "bg-green-100 text-green-700 border-green-300"
    default:
      return "bg-slate-100 text-slate-700 border-slate-300"
  }
}

export const getSeverityColor = (severity: string): string => {
  switch (severity) {
    case "severe":
      return "bg-red-100 text-red-700 border-red-300"
    case "moderate":
      return "bg-amber-100 text-amber-700 border-amber-300"
    case "mild":
      return "bg-blue-100 text-blue-700 border-blue-300"
    default:
      return "bg-slate-100 text-slate-700 border-slate-300"
  }
}

export const isPointInPolygon = (x: number, y: number, polygon: { x: number; y: number }[]): boolean => {
  let isInside = false
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].x,
      yi = polygon[i].y
    const xj = polygon[j].x,
      yj = polygon[j].y

    const intersect = yi > y != yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi
    if (intersect) isInside = !isInside
  }
  return isInside
}
