import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface AuthState {
  accessToken: string | null
  refreshToken: string | null
  email: string | null
  userId: string | null
}
const initialState: AuthState = {
  accessToken: null,
  refreshToken: null,
  email: null,
  userId: null,
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setAccessToken(state, action: PayloadAction<string>) {
      state.accessToken = action.payload
    },
    setRefreshToken(state, action: PayloadAction<string>) {
      state.refreshToken = action.payload
    },
    setUserInfo(
      state,
      action: PayloadAction<{ email: string; userId: string }>
    ) {
      state.email = action.payload.email
      state.userId = action.payload.userId
    },
    clearAuth(state) {
      Object.assign(state, initialState)
    },
  },
})

export const {
  setAccessToken,
  setRefreshToken,
  setUserInfo,
  clearAuth,
} = authSlice.actions

export default authSlice.reducer
