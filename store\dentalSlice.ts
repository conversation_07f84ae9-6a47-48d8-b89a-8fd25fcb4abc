import { createSlice, PayloadAction } from "@reduxjs/toolkit"

// Define types for our state
interface DentalState {
  // Dashboard state
  dateRange: string
  selectedDate: string | null
  endDate: string | null
  isCalendarOpen: boolean
  activeChart: string

  // Patient state
  selectedPatient: string | null
  searchQuery: string

  // KPI data
  kpiData: {
    newPatients: { value: number; change: number; trend: "up" | "down" }
    procedures: { value: number; change: number; trend: "up" | "down" }
    aiDiagnoses: { value: number; change: number; trend: "up" | "down" }
  }

  // Chart data
  patientFlowData: Array<{ date: string; patients: number }>

  // System status
  systemStatus: SystemStatus
}

interface SystemStatus {
  aiEngine: {
    status: "online" | "offline" | "degraded"
    processingLoad: number // percentage 0-100
    responseTime: number // in milliseconds
    lastUpdated: string
  }
  backend: {
    status: "healthy" | "unhealthy" | "maintenance"
    uptime: string
    lastHealthCheck: string
    responseTime: number // in milliseconds
  }
}

const initialState: DentalState = {
  // Dashboard state
  dateRange: "month",
  selectedDate: new Date().toISOString(),
  endDate: new Date().toISOString(),
  isCalendarOpen: false,
  activeChart: "patientFlow",

  // Patient state
  selectedPatient: null,
  searchQuery: "",

  // KPI data
  kpiData: {
    newPatients: { value: 42, change: 8.5, trend: "up" },
    procedures: { value: 327, change: 15.2, trend: "up" },
    aiDiagnoses: { value: 215, change: 42.8, trend: "up" },
  },

  // Chart data
  patientFlowData: [
    { date: "Jan 1", patients: 24 },
    { date: "Jan 2", patients: 18 },
    { date: "Jan 3", patients: 30 },
    { date: "Jan 4", patients: 26 },
    { date: "Jan 5", patients: 32 },
    { date: "Jan 6", patients: 28 },
    { date: "Jan 7", patients: 12 },
    { date: "Jan 8", patients: 27 },
    { date: "Jan 9", patients: 31 },
    { date: "Jan 10", patients: 29 },
    { date: "Jan 11", patients: 33 },
    { date: "Jan 12", patients: 25 },
    { date: "Jan 13", patients: 21 },
    { date: "Jan 14", patients: 15 },
  ],

  systemStatus: {
    aiEngine: {
      status: "online",
      processingLoad: 67,
      responseTime: 245,
      lastUpdated: new Date().toISOString(),
    },
    backend: {
      status: "healthy",
      uptime: "99.9%",
      lastHealthCheck: new Date().toISOString(),
      responseTime: 89,
    },
  },
}

const dentalSlice = createSlice({
  name: "dental",
  initialState,
  reducers: {
    // Dashboard actions
    setDateRange: (state, action: PayloadAction<string>) => {
      state.dateRange = action.payload
    },
    setSelectedDate: (state, action: PayloadAction<string | null>) => {
      state.selectedDate = action.payload
    },
    setEndDate: (state, action: PayloadAction<string | null>) => {
      state.endDate = action.payload
    },
    setCalendarOpen: (state, action: PayloadAction<boolean>) => {
      state.isCalendarOpen = action.payload
    },
    setActiveChart: (state, action: PayloadAction<string>) => {
      state.activeChart = action.payload
    },

    // Patient actions
    setSelectedPatient: (state, action: PayloadAction<string | null>) => {
      state.selectedPatient = action.payload
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload
    },

    // System status
    updateSystemStatus: (state, action: PayloadAction<Partial<SystemStatus>>) => {
      state.systemStatus = { ...state.systemStatus, ...action.payload }
    },
  },
});

export const {
  setDateRange,
  setSelectedDate,
  setEndDate,
  setCalendarOpen,
  setActiveChart,
  setSelectedPatient,
  setSearchQuery,
  updateSystemStatus,
} = dentalSlice.actions

export default dentalSlice.reducer; 
