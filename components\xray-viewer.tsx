"use client"

import { useRef, useEffect, useState } from "react"
import { useGesture } from "@use-gesture/react"
import type { Decay } from "./dental-xray-viewer"
import { Move } from "lucide-react"

interface XrayViewerProps {
  showAI: boolean
  zoom: number
  isPanActive: boolean
  selectedTooth: number | null
  currentXrayId: string
  decayFindings: Decay[]
}

export default function XrayViewer({
  showAI,
  zoom,
  isPanActive,
  selectedTooth,
  currentXrayId,
  decayFindings,
}: XrayViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [position, setPosition] = useState({ x: 0, y: 0 })

  // Reset position when zoom changes
  useEffect(() => {
    setPosition({ x: 0, y: 0 })
  }, [zoom])

  // Handle pan gestures
  useGesture(
    {
      onDrag: ({ movement: [x, y], first }) => {
        if (!isPanActive) return
        if (first) {
          // Save current position when starting drag
        }
        setPosition({ x, y })
      },
    },
    {
      target: containerRef,
      enabled: isPanActive,
    },
  )

  return (
    <div
      ref={containerRef}
      className="relative w-full h-full overflow-hidden bg-black/5 flex items-center justify-center"
    >
      <div
        className="relative transition-transform duration-100"
        style={{
          transform: `scale(${zoom / 100}) translate(${position.x}px, ${position.y}px)`,
        }}
      >
        {/* Placeholder for X-ray image */}
        <div className="relative">
          <img src="/placeholder.svg?key=ay3pe" alt="Dental X-ray" className="max-w-5xl" />

          {/* AI-highlighted overlay */}
          {showAI && (
            <div className="absolute inset-0 pointer-events-none">
              {/* Severe decay highlights */}
              <div
                className="absolute top-[35%] left-[28%] w-8 h-8 rounded-full bg-red-500/30 border-2 border-red-500 animate-pulse"
                style={{
                  opacity: selectedTooth === 18 || selectedTooth === null ? 1 : 0.3,
                  transform: selectedTooth === 18 ? "scale(1.2)" : "scale(1)",
                  transition: "all 0.2s ease-out",
                }}
              />

              {/* Moderate decay highlights */}
              <div
                className="absolute top-[42%] left-[35%] w-8 h-8 rounded-full bg-amber-500/30 border-2 border-amber-500"
                style={{
                  opacity: selectedTooth === 14 || selectedTooth === null ? 1 : 0.3,
                  transform: selectedTooth === 14 ? "scale(1.2)" : "scale(1)",
                  transition: "all 0.2s ease-out",
                }}
              />

              <div
                className="absolute top-[58%] left-[53%] w-8 h-8 rounded-full bg-amber-500/30 border-2 border-amber-500"
                style={{
                  opacity: selectedTooth === 31 || selectedTooth === null ? 1 : 0.3,
                  transform: selectedTooth === 31 ? "scale(1.2)" : "scale(1)",
                  transition: "all 0.2s ease-out",
                }}
              />

              <div
                className="absolute top-[32%] left-[65%] w-8 h-8 rounded-full bg-amber-500/30 border-2 border-amber-500"
                style={{
                  opacity: selectedTooth === 3 || selectedTooth === null ? 1 : 0.3,
                  transform: selectedTooth === 3 ? "scale(1.2)" : "scale(1)",
                  transition: "all 0.2s ease-out",
                }}
              />
            </div>
          )}
        </div>
      </div>

      {isPanActive && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/10 backdrop-blur-sm pointer-events-none">
          <div className="bg-white/80 rounded-full p-3">
            <Move size={24} className="text-slate-600" />
          </div>
        </div>
      )}
    </div>
  )
}
