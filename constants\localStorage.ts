export const loadState = () => {
    try {
        if (typeof window === 'undefined') return undefined; // Don't run on server
        const serializedState = localStorage.getItem("reduxState");
        if (!serializedState) return undefined;
        return JSON.parse(serializedState);
    } catch (err) {
        console.error("Load state error:", err);
        return undefined;
    }
};
 
export const saveState = (state: any) => {
    try {
        if (typeof window === 'undefined') return; // Don't run on server
        const serializedState = JSON.stringify(state);
        localStorage.setItem("reduxState", serializedState);
    } catch (err) {
        console.error("Save state error:", err);
    }
};