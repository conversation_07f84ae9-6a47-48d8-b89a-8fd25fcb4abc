///importsnt

"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Cookies from "js-cookie";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Mail, LogOut } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  Menu,
  Search,
  LayoutDashboard,
  Users,
  Settings,
  Smile,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";
import { useAppSelector } from "@/store/hooks";
import { useAuth } from "@/hooks/useAuth";
const baseNavigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
  { name: "Patients", href: "/patients", icon: Users },
  { name: "Settings", href: "/settings", icon: Settings },
];

export function TopNavigation() {
  const { resolvedTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const pathname = usePathname();
  const { toast } = useToast();
  const { logout, displayName, roleId, isAuthenticated } = useAuth();
  const logoSrc =
    resolvedTheme === "dark" ? "/JedAI-log-transparent.png" : "/JedAI-logo.png";

  const { emails } = useAppSelector((s) => s.user);
  console.log(
    "TopNavigation email:",
    emails,
    "displayName:",
    displayName,
    "roleId:",
    roleId
  );

  const name = displayName ?? emails?.split("@")[0] ?? "User";
  const initial = name.charAt(0).toUpperCase();

  // Don't render navigation if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }
  

  const handleSignOut = async () => {
    try {
      console.log("TopNavigation: Starting logout process");
      await logout();
    } catch (error) {
      console.error("TopNavigation logout error:", error);
      // Even if there's an error, try to force logout
      try {
        sessionStorage.clear();
        localStorage.clear();
        window.location.href = "/login";
      } catch (fallbackError) {
        console.error("Fallback logout failed:", fallbackError);
        toast({
          variant: "destructive",
          title: "Logout error",
          description: "Failed to logout. Please refresh the page and try again.",
        });
      }
    }
  };


  const navigation =
    roleId === 1
      ? baseNavigation.filter((item) => item.name === "Settings")
      : baseNavigation;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background">
      <div className="container flex h-16 items-center">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2 mb-2">
          <img src={logoSrc} alt="JED AI LOGO" className="h-[45px] w-auto" />

          {/* <img src="/JedAI-logo.png" className="h-[45px]" alt="JED AI LOGO"/> */}
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6 ml-8">
          {navigation.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                  pathname === item.href
                    ? "bg-accent text-accent-foreground"
                    : "text-muted-foreground"
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{item.name}</span>
              </Link>
            );
          })}
        </nav>

      
        {emails && (
          <div className="hidden md:flex ml-auto pr2">
          
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center space-x-2">
                  <span className="text-sm font-medium">Dr. {name}</span>
                  <div
                    className="h-8 w-8 rounded-full bg-gray-800 flex items-center justify-center text-white font-medium"
                    title={name}
                  >
                    {initial}
                  </div>
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                align="end"
                className="w-64 bg-white rounded-lg shadow-md overflow-hidden"
              >
            
                <div className="px-4 py-3 bg-gray-50">
                  <div className="flex items-center space-x-3">
           
                    <div className="flex flex-col">
                     
                      <span className="text-sm font-semibold">{emails}</span>
                    </div>
                  </div>
                </div>

                <DropdownMenuSeparator />

                
<div className="px-4">
  <a
    href="mailto:<EMAIL>"
    className="text-blue-600 underline hover:text-blue-800 text-sm"
  >
    Contact us
  </a>
</div>


                <DropdownMenuSeparator />

                {/* ─── Sign Out ─────────────────────────────────── */}
                <DropdownMenuItem
                  className="flex items-center space-x-2 text-red-600 hover:bg-red-50"
                  onSelect={(e) => {
                    e.preventDefault();
                    handleSignOut();
                  }}
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* Mobile Navigation */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-[300px] sm:w-[400px]">
            <nav className="flex flex-col space-y-4 mt-8">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsOpen(false)}
                    className={cn(
                      "flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-colors hover:bg-accent hover:text-accent-foreground min-h-[52px]",
                      pathname === item.href
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}

              {emails && (
                <div className="mt-6 px-4 border-t pt-4 flex items-center space-x-2">
                  <Smile className="h-5 w-5 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">
                    {emails}
                  </span>
                </div>
              )}
            </nav>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
}
