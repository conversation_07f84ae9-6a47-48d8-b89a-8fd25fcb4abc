import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';

interface VisitState {
  id: string | null;
}

const initialVisitState: VisitState = {
  id: Cookies.get('visitId') || null,
};

const visitSlice = createSlice({
  name: 'visit',
  initialState: initialVisitState,
  reducers: {
    setVisitId(state, action: PayloadAction<string>) {
      state.id = action.payload;
      Cookies.set('visitId', action.payload);
    },
    clearVisitId(state) {
      state.id = null;
      Cookies.remove('visitId');
    },
  },
});

export const { setVisitId, clearVisitId } = visitSlice.actions;
export const selectVisitId = (state: any) => state.visit.id;
export default visitSlice.reducer;