source env_variables.sh

docker build ./ -t $IMAGE_URI

gcloud auth login

docker push $IMAGE_URI

gcloud run deploy jedai-ui-app  --image $IMAGE_URI --platform managed  --region $REGION  --allow-unauthenticated --port 8080

Cloud Run -> jedai-db-api -> Edit & Deploy new version -> Containers (Cloud SQL connections) -> Networking (Use Serverless VPC Access connectors -> select the VPC endpoint for SQL) -> deploy

https://jedai-ui-app-714292203960.us-east1.run.app

To enable the cors for GCS bucket
gsutil cors set cors-config.json gs://jedai_bucket

To view thw cors settings
gsutil cors get gs://jedai_bucket



