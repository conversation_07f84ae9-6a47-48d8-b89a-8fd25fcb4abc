"use client";

import { useState } from "react";
import {
  Phone,
  Mail,
  MapPin,
  Shield,
  AlertCircle,
  Calendar,
  ChevronUp,
  ChevronDown,
  Upload,
  Play,
  Loader2,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { XrayImage } from "../constants";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { Progress } from "@/components/ui/progress";
interface Patient {
  id: string;
  name: string;
  dob: string;
  phone: string;
  email: string;
  address?: string;
  insurance?: string;
  emergencyContact?: string;
}

interface PatientHeaderProps {
  patient?: Patient | null; // Make patient optional
  compact?: boolean;
  isUploading?:boolean;
  uploadStatus:any;
  uploadProgress:any;
  onBulkUploadClick: () => void;
  onRunAnalysisClick: () => void;
  isAnalyzing:any;
}

export function PatientHeader({
  patient,
  compact = false,
  isUploading,
  isAnalyzing,
  uploadStatus,
  uploadProgress,
  onBulkUploadClick,
  onRunAnalysisClick,
}: PatientHeaderProps) {
  const [isExpanded, setIsExpanded] = useState(!compact);
  console.log("PatientHeader rendered with patient:", patient);
 
  // Safe access to patient properties
  const initials = patient?.name?.charAt(0) || "?";
  const displayName = patient?.name || "Loading...";
  const displayId = patient?.id ? `ID: ${patient.id}` : "";


  // grab slots & globalAnalyzing straight from Redux
  const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
  console.log("slots", slots);
  const globalAnalyzing = useSelector(
    (state: RootState) => state.fullMouthSeries.globalAnalyzing
  );
  console.log("globalAnalyzing", globalAnalyzing);

  const uploadedCount = Object.values(slots).filter(Boolean).length;
  const hasImages = uploadedCount > 0;

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center text-gray-500 text-xl font-semibold">
              {initials}
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">{displayName}</h1>
              {displayId && (
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>{displayId}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant={uploadedCount === 0 ? "default" : "outline"}
              size="sm"
              className="transition-all bg-blue-600 hover:bg-blue-700 text-white hover:text-white shadow-lg animate-pulse"
              onClick={onBulkUploadClick}
            >
              <Upload className="w-4 h-4 mr-1" />
              Bulk Upload
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={onRunAnalysisClick}
              disabled={!hasImages || isAnalyzing}
              className={`transition-all ${
                hasImages && !isAnalyzing
                  ? "bg-green-600 hover:bg-green-700 text-white shadow-lg animate-pulse"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {isAnalyzing ? (
                <Loader2 className="w-4 h-4 mr-1 animate-spin" />
              ) : (
                <Play className="w-4 h-4 mr-1" />
              )}
              Run AI Analysis
            </Button>
          </div>
        </div>
        {/* {isUploading && (
        <div className="top-2 left-0 right-0 z-50">
          <div className="bg-white p-2 shadow-md">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">
                {uploadStatus || "Uploading images..."}
              </span>
              <span className="text-sm font-medium text-gray-700">
                {Math.round(uploadProgress)}%
              </span>
            </div>
            <Progress value={uploadProgress} className="h-2" />
          </div>
        </div>
        
      )} */}
      </div>
      
    </div>
  );
}
