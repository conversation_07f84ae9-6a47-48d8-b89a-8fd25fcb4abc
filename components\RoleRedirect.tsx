"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

const RoleRedirect = () => {
    const router = useRouter();

    useEffect(() => {
        const getCookie = (key: string) =>
            document.cookie
                .split("; ")
                .find((row) => row.startsWith(`${key}=`))
                ?.split("=")[1];

        const isLoggedIn = getCookie("auth-session") === "true";
        const email = decodeURIComponent(getCookie("email") || "");

        if (!isLoggedIn) {
            router.replace("/login");
        } else if (email === "<EMAIL>") {
            router.replace("/usermanagement");
        } else {
            router.replace("/dashboard");
        }
    }, [router]);

    return <div>Redirecting based on your role...</div>;
};

export default RoleRedirect;
