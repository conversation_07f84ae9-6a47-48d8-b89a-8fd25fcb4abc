"use client";

import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/store";

import type { XrayImage, Annotation } from "../patients/[id]/types";
import { AIAnnotator } from "../patients/[id]/components/ai-annotator";
import { clearSelectedAnnotatorImage } from "@/store/fullMouthSeriesSlice";

export default function AIAnnotatorPage() {
    const router = useRouter();
    const dispatch = useDispatch(); // Initialize dispatch
    const [selectedImage, setSelectedImage] = useState<{
        slotId: string;
        image: XrayImage;
    } | null>(null);
    const [annotations, setAnnotations] = useState<Record<string, Annotation>>({});
    const [isLoading, setIsLoading] = useState(true);
    const [runAnalysis, setRunAnalysis] = useState<() => Promise<void>>(() => async () => {});
    // Get data from Redux store
    const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
    const panoramicImage = useSelector((state: RootState) => state.fullMouthSeries.panoramicImage);
    const globalAnnotations = useSelector((state: RootState) => state.fullMouthSeries.annotations);
    const selectedAnnotatorImage = useSelector((state: RootState) => state.fullMouthSeries.selectedAnnotatorImage); // Get from Redux

    useEffect(() => {
        // Use selectedAnnotatorImage from Redux
        if (selectedAnnotatorImage) {
            setSelectedImage(selectedAnnotatorImage);
            setAnnotations(globalAnnotations);
        } else {
            // Fallback: try to get current image from Redux if available (should ideally not be needed if selectedAnnotatorImage is set correctly)
            const firstSlot = Object.keys(slots)[0];
            if (firstSlot && slots[firstSlot]) {
                setSelectedImage({ slotId: firstSlot, image: slots[firstSlot]! });
                setAnnotations(globalAnnotations);
            } else if (panoramicImage) {
                setSelectedImage({ slotId: 'PANORAMIC', image: panoramicImage });
                setAnnotations(globalAnnotations);
            }
        }
        setIsLoading(false);
    }, [slots, panoramicImage, globalAnnotations, selectedAnnotatorImage]); // Add selectedAnnotatorImage to deps

    const handleClose = () => {
        // Enhanced: Clear selected annotator image from Redux to trigger detection
        console.log("🔙 Clearing selectedAnnotatorImage and navigating back...");
        dispatch(clearSelectedAnnotatorImage());
        router.back();
    };

    // This will be called by the AIAnnotator component when closing
    const handleCloseWithSave = () => {
        // The AIAnnotator component will handle saving annotations internally
        // We just need to handle the navigation
        handleClose();
    };

    const registerRunAnalysis = useCallback(
    (fn: () => Promise<void>) => {
      setRunAnalysis(() => fn);
    },
    [] // no deps → identity stays the same
  );

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    if (!selectedImage) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-lg">No image selected</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <AIAnnotator
                runAnalysis={runAnalysis}
                isOpen={true}
                onClose={handleCloseWithSave}
                image={selectedImage.image}
                slotInfo={{
                    id: selectedImage.slotId,
                    name: "Selected Image"
                }}
                annotations={annotations[selectedImage.slotId] || globalAnnotations[selectedImage.slotId]}
                router={router}
                isFullScreen={true}
                allImages={{ slots, panoramicImage }}
            />
        </div>
    );
}
