import { configureStore, combineReducers } from "@reduxjs/toolkit";
import dentalReducer from "./dentalSlice";
import visitsReducer from "./visitsSlice";
import dashboardReducer from "./dashboardSlice";
import visitReducer from "./visitSlice";
import patientReducer from "./patientSlice";
import previousVisitReducer from "./previousVisits";
import { loadState, saveState } from "../constants/localStorage";
import authReducer from "./authSlice";
import userReducer from "./userSlice";
import fullMouthSeriesReducer from './fullMouthSeriesSlice'


// ✅ Step 1: Combine reducers
const rootReducer = combineReducers({
  dental: dentalReducer,
  visits: visitsReducer,
  dashboard: dashboardReducer,
  visit: visitReducer,
  patient: patientReducer,
  previousVisit: previousVisitReducer,
  user: userReducer,
  auth: authReducer,
  fullMouthSeries: fullMouthSeriesReducer,
});

// ✅ Step 2: Define RootState type from rootReducer
export type RootState = ReturnType<typeof rootReducer>;

// ✅ Step 3: Load from localStorage with correct typing
const preloadedState = loadState() as Partial<RootState>;

// ✅ Step 4: Configure store
export const store = configureStore({
  reducer: rootReducer,
  preloadedState,
});

// ✅ Step 5:
store.subscribe(() => {
  saveState(store.getState());
});

// ✅ Export dispatch type
export type AppDispatch = typeof store.dispatch;